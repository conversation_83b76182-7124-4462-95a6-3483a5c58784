{"openapi": "3.1.0", "info": {"title": "Miluo Framework RESTful APIs", "description": "咪啰Java脚手架接口说明文档", "contact": {"name": "<PERSON><PERSON><PERSON>"}, "version": "3.2.0"}, "externalDocs": {"description": "参考文档", "url": "https://github.com/swagger-api/swagger-core/wiki/Swagger-2.X---Annotations"}, "servers": [{"url": "http://localhost:8080/tps-permission-service", "description": "Generated server url"}], "security": [{"JWT": []}], "tags": [{"name": "<PERSON><PERSON>", "description": "菜单管理"}, {"name": "User", "description": "用户管理"}, {"name": "Role", "description": "角色管理"}], "paths": {"/v1/role/bindPermission": {"put": {"tags": ["Role"], "summary": "绑定权限数据", "description": "绑定权限数据", "operationId": "bindPermission", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleBindPermissionCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/verifyUserPassword": {"post": {"tags": ["User"], "summary": "当前密码校验", "description": "当前密码校验", "operationId": "verifyUserPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyUserPasswordCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/updateUser": {"post": {"tags": ["User"], "summary": "更新用户", "description": "更新用户基本信息", "operationId": "updateUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/resetPassword": {"post": {"tags": ["User"], "summary": "重置用户密码", "description": "重置用户密码", "operationId": "resetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResetPasswordCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/findUserPage": {"post": {"tags": ["User"], "summary": "获取用户信息", "description": "根据通用查询获取用户信息", "operationId": "findUserPage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedApiResultUserQryCO"}}}}}}}, "/v1/user/findUserGrantedMenuUITree": {"post": {"tags": ["User"], "summary": "查询授权的界面权限", "description": "查询授权的界面权限", "operationId": "findUserGrantedMenuUITree", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGrantedMenuUITreeQry"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultMenuUICO"}}}}}}}, "/v1/user/findCurrentUser": {"post": {"tags": ["User"], "summary": "获取当前登录用户信息", "description": "根据当前用户token获取用户信息", "operationId": "findCurrentUser", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SingleApiResultUserCO"}}}}}}}, "/v1/user/findCurrentUserGrantedUIPermissions": {"post": {"tags": ["User"], "summary": "查询用户授权的界面权限码集合", "description": "查询用户授权的界面权限码集合", "operationId": "findCurrentUserGrantedUIPermissions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultString"}}}}}}}, "/v1/user/exportUserList": {"post": {"tags": ["User"], "summary": "导出用户信息", "description": "通过现有查询导出用户信息", "operationId": "exportUserList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/v1/user/disableUserList": {"post": {"tags": ["User"], "summary": "批量禁用用户", "description": "批量禁用用户信息", "operationId": "disableUserList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDisableCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/changePassword": {"post": {"tags": ["User"], "summary": "修改用户密码", "description": "修改用户密码", "operationId": "changePassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChangePasswordCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/addUser": {"post": {"tags": ["User"], "summary": "添加新用户", "description": "添加新用户", "operationId": "addUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SingleApiResultLong"}}}}}}}, "/v1/user/activeUserList": {"post": {"tags": ["User"], "summary": "批量激活用户", "description": "批量激活用户信息", "operationId": "activeUserList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserActiveCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/role/updateRole": {"post": {"tags": ["Role"], "summary": "更新角色", "description": "更新角色", "operationId": "updateRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpdateCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/role/findRolePage": {"post": {"tags": ["Role"], "summary": "获取角色信息", "description": "根据通用查询获取角色信息", "operationId": "findRolePage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedApiResultRoleCO"}}}}}}}, "/v1/role/findRoleList": {"post": {"tags": ["Role"], "summary": "获取所有角色信息", "description": "获取角色全集", "operationId": "findRoleList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultRoleCO"}}}}}}}, "/v1/role/findRoleGrantedMenuUITree": {"post": {"tags": ["Role"], "summary": "查询角色绑定权限", "description": "查询角色绑定权限", "operationId": "findRoleGrantedMenuUITree", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleGrantedMenuUITreeQry"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultMenuUICO"}}}}}}}, "/v1/role/findRoleBoundPermissionSet": {"post": {"tags": ["Role"], "summary": "获取角色已绑定的权限信息", "description": "获取角色已绑定的权限信息", "operationId": "findRoleBoundPermissionSet", "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleBoundPermissionQryCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultLong"}}}}}}}, "/v1/role/deleteRoleList": {"post": {"tags": ["Role"], "summary": "删除角色信息", "description": "删除角色信息", "operationId": "deleteRoleList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolesDeleteCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/role/addRole": {"post": {"tags": ["Role"], "summary": "新增角色", "description": "新增角色", "operationId": "addRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAddCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SingleApiResultRoleCO"}}}}}}}, "/v1/menu/updatePermissionUI": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "更新界面权限", "description": "更新界面权限", "operationId": "updatePermissionUI", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionUIUpdateCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/menu/reOrderPermissionUI": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "排序界面权限", "description": "排序界面权限", "operationId": "reOrderPermissionUI", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionUIReOrderCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/menu/findPermissionUIPage": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "获取界面权限信息", "description": "根据通用查询获取界面权限信息", "operationId": "findPermissionUIPage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedApiResultPermissionUICO"}}}}}}}, "/v1/menu/findMenuTree": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "获取菜单信息", "description": "获取树状菜单信息", "operationId": "findMenuTree", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultMenuCO"}}}}}}}, "/v1/menu/delPermissionUI": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "删除界面权限", "description": "删除界面权限", "operationId": "delPermissionUI", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionUIDelCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/menu/addPermissionUI": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "新增界面权限", "description": "新增界面权限", "operationId": "addPermissionUI", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionUIAddCmd"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResult"}}}}}}}, "/v1/user/findUserByName": {"get": {"tags": ["User"], "summary": "获取单个用户信息", "description": "根据用户登录名获取用户信息", "operationId": "findUserByName", "parameters": [{"name": "username", "in": "query", "description": "用户登录名", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SingleApiResultUserCO"}}}}}}}, "/v1/role/findMenuList": {"get": {"tags": ["Role"], "summary": "获取菜单权限列表", "description": "获取菜单权限列表", "operationId": "findMenuList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiApiResultMenuUICO"}}}}}}}}, "components": {"schemas": {"RoleBindPermissionCmd": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int64"}, "addedPermissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}, "removedPermissionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["addedPermissionIds", "roleId"]}, "ApiResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}}}, "VerifyUserPasswordCmd": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "password": {"type": "string", "description": "密码"}}, "required": ["password"]}, "UserUpdateCO": {"type": "object", "properties": {"extValues": {"type": "object", "additionalProperties": {}}, "id": {"type": "integer", "format": "int64", "description": "用户id"}, "name": {"type": "string", "description": "用户登录名"}, "fullName": {"type": "string", "description": "用户名字"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}, "email": {"type": "string", "description": "邮箱"}, "phoneNumber": {"type": "string", "description": "手机号码"}, "enabled": {"type": "boolean", "description": "是否可用"}, "roleIds": {"type": "array", "description": "角色ids", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["email", "enabled", "name"]}, "UserUpdateCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "userUpdateCO": {"$ref": "#/components/schemas/UserUpdateCO", "description": "用户CO"}}, "required": ["userUpdateCO"]}, "UserResetPasswordCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "userId": {"type": "integer", "format": "int64", "description": "重置密码的用户id"}}, "required": ["userId"]}, "PageCriteria": {"type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "SimpleQueryRequest": {"type": "object", "properties": {"filter": {"type": "object", "additionalProperties": {}}, "sorter": {"type": "object", "additionalProperties": {"type": "string"}}, "pagination": {"$ref": "#/components/schemas/PageCriteria"}}}, "PagedApiResultUserQryCO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "total": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserQryCO"}}}}, "UserQryCO": {"type": "object", "properties": {"extValues": {"type": "object", "additionalProperties": {}}, "id": {"type": "integer", "format": "int64", "description": "用户id"}, "name": {"type": "string", "description": "用户登录名"}, "fullName": {"type": "string", "description": "用户名字"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}, "email": {"type": "string", "description": "邮箱"}, "phoneNumber": {"type": "string", "description": "手机号码"}, "enabled": {"type": "boolean", "description": "是否可用"}, "creator": {"type": "string", "description": "创建人"}, "modifier": {"type": "string", "description": "修改人"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "最后一次修改时间"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "登录时间"}, "lastLogoutTime": {"type": "string", "format": "date-time", "description": "登出时间"}, "roleIds": {"type": "array", "description": "角色ids", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["email", "enabled", "name"]}, "UserGrantedMenuUITreeQry": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}}, "required": ["userId"]}, "MenuUICO": {"type": "object", "properties": {"permissionId": {"type": "integer", "format": "int64", "description": "权限标识"}, "parenPermissionId": {"type": "integer", "format": "int64", "description": "权限标识"}, "code": {"type": "string", "description": "代码"}, "parentCode": {"type": "string", "description": "父代码"}, "name": {"type": "string", "description": "名称"}, "type": {"type": "integer", "format": "int32", "description": "类型：0：菜单 1：菜单项目 2：UI项"}, "children": {"description": "子菜单"}}}, "MultiApiResultMenuUICO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MenuUICO"}}, "empty": {"type": "boolean"}, "notEmpty": {"type": "boolean"}}}, "SingleApiResultUserCO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserCO"}}}, "UserCO": {"type": "object", "properties": {"extValues": {"type": "object", "additionalProperties": {}}, "id": {"type": "integer", "format": "int64", "description": "用户id"}, "name": {"type": "string", "description": "用户登录名"}, "fullName": {"type": "string", "description": "用户名字"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}, "email": {"type": "string", "description": "邮箱"}, "phoneNumber": {"type": "string", "description": "手机号码"}, "enabled": {"type": "boolean", "description": "是否可用"}, "mobilePhone": {"type": "string", "description": "手机号"}, "creator": {"type": "string", "description": "创建人"}, "modifier": {"type": "string", "description": "修改人"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后一次登录时间"}, "lastLogoutTime": {"type": "string", "format": "date-time", "description": "登出时间"}, "passwordModifyTime": {"type": "string", "format": "date-time", "description": "上次密码修改时间"}, "roleIds": {"type": "array", "description": "角色ids", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["email", "enabled", "name"]}, "MultiApiResultString": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "string"}}, "empty": {"type": "boolean"}, "notEmpty": {"type": "boolean"}}}, "UserDisableCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "userIds": {"type": "array", "description": "用户Id", "items": {"type": "integer", "format": "int64"}}}, "required": ["userIds"]}, "ChangeUserPasswordCO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "oldPassword": {"type": "string"}, "newPassword": {"type": "string"}}, "required": ["id", "newPassword", "oldPassword"]}, "UserChangePasswordCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "changeUserPasswordCO": {"$ref": "#/components/schemas/ChangeUserPasswordCO", "description": "修改密码CO"}}, "required": ["changeUserPasswordCO"]}, "UserAddCO": {"type": "object", "properties": {"extValues": {"type": "object", "additionalProperties": {}}, "name": {"type": "string", "description": "用户登录名"}, "fullName": {"type": "string", "description": "用户名字"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}, "email": {"type": "string", "description": "邮箱"}, "phoneNumber": {"type": "string", "description": "手机号码"}, "enabled": {"type": "boolean", "description": "是否可用"}, "roleIds": {"type": "array", "description": "角色id", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}}, "required": ["email", "enabled", "name"]}, "UserAddCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "userAddCO": {"$ref": "#/components/schemas/UserAddCO", "description": "用户CO"}}, "required": ["userAddCO"]}, "SingleApiResultLong": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int64"}}}, "UserActiveCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "userIds": {"type": "array", "description": "用户Id", "items": {"type": "integer", "format": "int64"}}}, "required": ["userIds"]}, "RoleCO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "角色Id"}, "code": {"type": "string", "description": "代码"}, "name": {"type": "string", "description": "角色名称"}, "creator": {"type": "string", "description": "创建人"}, "modifier": {"type": "string", "description": "修改人"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}}, "required": ["code", "name"]}, "RoleUpdateCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "roleCO": {"$ref": "#/components/schemas/RoleCO", "description": "用户CO"}}, "required": ["roleCO"]}, "PagedApiResultRoleCO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "total": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleCO"}}}}, "MultiApiResultRoleCO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleCO"}}, "empty": {"type": "boolean"}, "notEmpty": {"type": "boolean"}}}, "RoleGrantedMenuUITreeQry": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int64"}, "granted": {"type": "boolean"}}}, "RoleBoundPermissionQryCmd": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int64", "description": "角色id"}, "type": {"type": "string", "description": "类型"}}, "required": ["roleId"]}, "MultiApiResultLong": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "empty": {"type": "boolean"}, "notEmpty": {"type": "boolean"}}}, "RolesDeleteCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "roleIds": {"type": "array", "description": "用户Id", "items": {"type": "integer", "format": "int64"}}}, "required": ["roleIds"]}, "RoleAddCmd": {"type": "object", "properties": {"operater": {"type": "string"}, "needsOperator": {"type": "boolean"}, "roleCO": {"$ref": "#/components/schemas/RoleCO", "description": "用户CO"}}, "required": ["roleCO"]}, "SingleApiResultRoleCO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/RoleCO"}}}, "PermissionUICO": {"type": "object", "properties": {"extValues": {"type": "object", "additionalProperties": {}}, "creator": {"type": "string", "description": "创建人"}, "modifier": {"type": "string", "description": "修改人"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "id": {"type": "integer", "format": "int64", "description": "标识"}, "code": {"type": "string", "description": "编码"}, "name": {"type": "string", "description": "名称"}, "sequence": {"type": "integer", "format": "int32", "description": "顺序"}, "menuId": {"type": "integer", "format": "int64", "description": "菜单标识"}, "permissionId": {"type": "integer", "format": "int64", "description": "permission_id"}}, "required": ["id"]}, "PermissionUIUpdateCmd": {"type": "object", "properties": {"permissionUICO": {"$ref": "#/components/schemas/PermissionUICO"}}, "required": ["permissionUICO"]}, "PermissionUIReOrderCmd": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PagedApiResultPermissionUICO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "total": {"type": "integer", "format": "int32"}, "current": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionUICO"}}}}, "MenuCO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "菜单标识"}, "code": {"type": "string", "description": "菜单代码"}, "name": {"type": "string", "description": "菜单名称"}, "routePath": {"type": "string", "description": "路由路径"}, "type": {"type": "string", "description": "菜单类型"}, "sequence": {"type": "integer", "format": "int32", "description": "顺序"}, "children": {"description": "子菜单"}}}, "MultiApiResultMenuCO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MenuCO"}}, "empty": {"type": "boolean"}, "notEmpty": {"type": "boolean"}}}, "PermissionUIDelCmd": {"type": "object", "properties": {"permissionUIId": {"type": "integer", "format": "int64"}, "permissionId": {"type": "integer", "format": "int64"}}, "required": ["permissionUIId"]}, "PermissionUIAddCmd": {"type": "object", "properties": {"permissionUICO": {"$ref": "#/components/schemas/PermissionUICO"}}, "required": ["permissionUICO"]}}, "securitySchemes": {"JWT": {"type": "http", "in": "header", "scheme": "bearer"}}}}