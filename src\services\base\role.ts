// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 新增角色 新增角色 POST /v1/role/addRole */
export async function addRole(body: API.RoleAddCmd, options?: { [key: string]: any }) {
  return request<API.SingleApiResultRoleCO>(`/base/v1/role/addRole`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 绑定权限数据 绑定权限数据 PUT /v1/role/bindPermission */
export async function bindPermission(
  body: API.RoleBindPermissionCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/role/bindPermission`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除角色信息 删除角色信息 POST /v1/role/deleteRoleList */
export async function deleteRoleList(body: API.RolesDeleteCmd, options?: { [key: string]: any }) {
  return request<API.ApiResult>(`/base/v1/role/deleteRoleList`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取菜单权限列表 获取菜单权限列表 GET /v1/role/findMenuList */
export async function findMenuList(options?: { [key: string]: any }) {
  return request<API.MultiApiResultMenuUICO>(`/base/v1/role/findMenuList`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取角色已绑定的权限信息 获取角色已绑定的权限信息 POST /v1/role/findRoleBoundPermissionSet */
export async function findRoleBoundPermissionSet(
  body: API.RoleBoundPermissionQryCmd,
  options?: { [key: string]: any },
) {
  return request<API.MultiApiResultLong>(`/base/v1/role/findRoleBoundPermissionSet`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询角色绑定权限 查询角色绑定权限 POST /v1/role/findRoleGrantedMenuUITree */
export async function findRoleGrantedMenuUiTree(
  body: API.RoleGrantedMenuUITreeQry,
  options?: { [key: string]: any },
) {
  return request<API.MultiApiResultMenuUICO>(`/base/v1/role/findRoleGrantedMenuUITree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有角色信息 获取角色全集 POST /v1/role/findRoleList */
export async function findRoleList(options?: { [key: string]: any }) {
  return request<API.MultiApiResultRoleCO>(`/base/v1/role/findRoleList`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取角色信息 根据通用查询获取角色信息 POST /v1/role/findRolePage */
export async function findRolePage(body: API.SimpleQueryRequest, options?: { [key: string]: any }) {
  return request<API.PagedApiResultRoleCO>(`/base/v1/role/findRolePage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新角色 更新角色 POST /v1/role/updateRole */
export async function updateRole(body: API.RoleUpdateCmd, options?: { [key: string]: any }) {
  return request<API.ApiResult>(`/base/v1/role/updateRole`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
