import dayjs from 'dayjs'
import { subtr } from "./dataOperation"

/*
 * @Description: 
 * @Author: sr
 * @Date: 2021-12-02 11:15:31
 */
export function sorterFuc(a: any, b: any, dataIndex: any) {
  // console.log('1111', dayjs(a[dataIndex]) , dayjs(b[dataIndex]))
  const exp1 = a[dataIndex]?.toString() || 0;
  const exp2 = b[dataIndex]?.toString() || 0;
  if(exp1 === 0) {
    return -1;
  }
  if(exp2 === 0) {
    return 1;
  }
  if(exp2 === 0 && exp1 === 0 ) {
    return 0;
  }
  return exp1 - exp2 || subtr(exp1,exp2)   
    || exp1?.localeCompare(exp2) 
    || dayjs(exp1).valueOf() > (dayjs(exp2).valueOf())
}

export function saveDot2(text: any) {
  // console.log(text);
  if(text === undefined || text === null || text === 0){
    return 0;
  }
  if (Math.floor(text) === text) {
    return text;
  }
  return (<span>{text.toFixed(2)}</span>)
}

/**
 * 获取第一个表格的可视化高度
 * @param {*} extraHeight 额外的高度(表格底部的内容高度 Number类型,默认为74) 
 * @param {*} id 当前页面中有多个table时需要制定table的id
 */
export function getTableScroll(extraHeight?: any, id?: any) {
  if (typeof extraHeight == "undefined") {
    //  默认底部分页64 + 边距10
    extraHeight = 74
  }
  let tHeader = null
  if (id) {
    tHeader = document.getElementById(id) ?
      document.getElementById(id)?.getElementsByClassName("ant-table-thead")[0] : null
  } else {
    tHeader = document.getElementsByClassName("ant-table-thead")[0]
  }
  //表格内容距离顶部的距离
  let tHeaderBottom = 0
  if (tHeader) {
    tHeaderBottom = tHeader.getBoundingClientRect().bottom
  }
  //窗体高度-表格内容顶部的高度-表格内容底部的高度
  // let height = document.body.clientHeight - tHeaderBottom - extraHeight
  let height = `calc(100vh - ${tHeaderBottom + extraHeight}px)`
  return height
}