import React from 'react';
import ReactEcharts from 'echarts-for-react';
import EchartsThemebBlue, { blueThemeName } from '@/utils/EchartsThemebBlue';
import * as echarts from 'echarts';

echarts.registerTheme(blueThemeName, EchartsThemebBlue());

// const data = [
//     {
//         "truckNo": "T450",
//         "operationTime": "2024-06-01 09:00:00",
//         "position": "H101-100914",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "UETU2778786",
//         "containerTwoNumber": "SITU2798474",
//         "movingDistance": "2670.0"
//     },
//     {
//         "truckNo": "T451",
//         "operationTime": "2024-06-01 09:34:03",
//         "position": "H101-340482",
//         "operationType": "卸船",
//         "containerSize": "40",
//         "containerOneNumber": "DFSU1274885",
//         "containerTwoNumber": "",
//         "movingDistance": "2170.0"
//     },
//     {
//         "truckNo": "T451",
//         "operationTime": "2024-06-01 09:39:11",
//         "position": "H101-261182",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "FTAU1011416",
//         "containerTwoNumber": "UETU2659278",
//         "movingDistance": "2159.0"
//     },
//     {
//         "truckNo": "T451",
//         "operationTime": "2024-06-01 09:51:11",
//         "position": "H101-140882",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "FTAU1011416",
//         "containerTwoNumber": "UETU2659278",
//         "movingDistance": "2239.0"
//     },
//     {
//         "truckNo": "T451",
//         "operationTime": "2024-06-03 09:00:11",
//         "position": "160982-H102",
//         "operationType": "装船",
//         "containerSize": "20",
//         "containerOneNumber": "FTAU1011416",
//         "containerTwoNumber": "UETU2659278",
//         "movingDistance": "2099.0"
//     },
//     {
//         "truckNo": "T451",
//         "operationTime": "2024-06-04 09:50:20",
//         "position": "340682-H103",
//         "operationType": "装船",
//         "containerSize": "40",
//         "containerOneNumber": "UQEU2745786",
//         "containerTwoNumber": "",
//         "movingDistance": "2210.0"
//     },
//     {
//         "truckNo": "T452",
//         "operationTime": "2024-06-01 10:39:11",
//         "position": "H101-120182",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TQDX2549459",
//         "containerTwoNumber": "UEAZ2778786",
//         "movingDistance": "2187.0"
//     },
//     {
//         "truckNo": "T452",
//         "operationTime": "2024-06-01 10:49:11",
//         "position": "150362-H101",
//         "operationType": "装船",
//         "containerSize": "20",
//         "containerOneNumber": "TGBU2549459",
//         "containerTwoNumber": "UETU2778786",
//         "movingDistance": "2187.0"
//     },
//     {
//         "truckNo": "T452",
//         "operationTime": "2024-06-01 10:55:11",
//         "position": "H101-170182",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TTHJ2549459",
//         "containerTwoNumber": "UDBN2778786",
//         "movingDistance": "2187.0"
//     },
//     {
//         "truckNo": "T453",
//         "operationTime": "2024-06-01 11:02:01",
//         "position": "H101-120543",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TZXU2549459",
//         "containerTwoNumber": "YUTU2778786",
//         "movingDistance": "2187.0"
//     },
//     {
//         "truckNo": "T453",
//         "operationTime": "2024-06-01 11:09:11",
//         "position": "H101-130482",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TQBU2549459",
//         "containerTwoNumber": "UETU2778786",
//         "movingDistance": "2187.0"
//     },
//     {
//         "truckNo": "T454",
//         "operationTime": "2024-06-01 11:20:58",
//         "position": "H101-190382",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TGCU0188113",
//         "containerTwoNumber": "GAOU2060329",
//         "movingDistance": "2157.0"
//     },
//     {
//         "truckNo": "T455",
//         "operationTime": "2024-06-01 09:41:58",
//         "position": "H102-110382",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TLLU3138957",
//         "containerTwoNumber": "SITU2860014",
//         "movingDistance": "2275.0"
//     },
//     {
//         "truckNo": "T455",
//         "operationTime": "2024-06-01 09:47:58",
//         "position": "H102-130482",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TQAU3138957",
//         "containerTwoNumber": "SIRT2860014",
//         "movingDistance": "2275.0"
//     },
//     {
//         "truckNo": "T455",
//         "operationTime": "2024-06-01 09:51:58",
//         "position": "H102-110342",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "YUIU3138957",
//         "containerTwoNumber": "AXVU2860014",
//         "movingDistance": "2275.0"
//     },
//     {
//         "truckNo": "T456",
//         "operationTime": "2024-06-01 10:00:46",
//         "position": "H102-190584",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "QWEU5304939",
//         "containerTwoNumber": "BUIU6454923",
//         "movingDistance": "2184.0"
//     },
//     {
//         "truckNo": "T457",
//         "operationTime": "2024-06-01 10:14:46",
//         "position": "H102-130931",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "UETU2457301",
//         "containerTwoNumber": "FCIU6193756",
//         "movingDistance": "2133.0"
//     },
//     {
//         "truckNo": "T458",
//         "operationTime": "2024-06-01 10:22:31",
//         "position": "H102-260782",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "FCIU4142092",
//         "containerTwoNumber": "FSDU4010584",
//         "movingDistance": "2455.0"
//     },
//     {
//         "truckNo": "T459",
//         "operationTime": "2024-06-01 10:29:55",
//         "position": "H102-270608",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TRTU2916774",
//         "containerTwoNumber": "IETU2147163",
//         "movingDistance": "1866.0"
//     },
//     {
//         "truckNo": "T459",
//         "operationTime": "2024-06-02 10:15:55",
//         "position": "H103-270608",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TOPU2916774",
//         "containerTwoNumber": "TUTU2147163",
//         "movingDistance": "1866.0"
//     },
//     {
//         "truckNo": "T459",
//         "operationTime": "2024-06-02 10:03:55",
//         "position": "H102-270608",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TRHU2916774",
//         "containerTwoNumber": "UETU2147163",
//         "movingDistance": "1866.0"
//     },
//     {
//         "truckNo": "T460",
//         "operationTime": "2024-06-02 09:34:03",
//         "position": "H102-250608",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "UETU2398024",
//         "containerTwoNumber": "FCIU5392835",
//         "movingDistance": "1987.0"
//     },
//     {
//         "truckNo": "T461",
//         "operationTime": "2024-06-01 09:37:03",
//         "position": "H102-270408",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "SNBU2261532",
//         "containerTwoNumber": "DFSU1009032",
//         "movingDistance": "2268.0"
//     },
//     {
//         "truckNo": "T462",
//         "operationTime": "2024-06-03 09:43:03",
//         "position": "H102-250408",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "UETU2418563",
//         "containerTwoNumber": "FCIU4143859",
//         "movingDistance": "2147.0"
//     },
//     {
//         "truckNo": "T462",
//         "operationTime": "2024-06-03 09:49:03",
//         "position": "270606-H102",
//         "operationType": "装船",
//         "containerSize": "40",
//         "containerOneNumber": "TWFD7836003",
//         "containerTwoNumber": "",
//         "movingDistance": "2110.0"
//     },
//     {
//         "truckNo": "T462",
//         "operationTime": "2024-06-03 09:55:03",
//         "position": "H102-180245",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TGBU7836003",
//         "containerTwoNumber": "TWXU8031372",
//         "movingDistance": "2110.0"
//     },
//     {
//         "truckNo": "T464",
//         "operationTime": "2024-06-03 09:51:11",
//         "position": "H102-250606",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "TTNU8363042",
//         "containerTwoNumber": "SEGU9991893",
//         "movingDistance": "2160.0"
//     },
//     {
//         "truckNo": "T465",
//         "operationTime": "2024-06-04 09:57:11",
//         "position": "H102-270406",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "SNBU2261532",
//         "containerTwoNumber": "DFSU1009032",
//         "movingDistance": "2340.0"
//     },
//     {
//         "truckNo": "T465",
//         "operationTime": "2024-06-04 10:06:11",
//         "position": "H102-210106",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "STYU2261532",
//         "containerTwoNumber": "DQWU1009032",
//         "movingDistance": "2340.0"
//     },
//     {
//         "truckNo": "T466",
//         "operationTime": "2024-06-04 09:34:03",
//         "position": "250406-H102",
//         "operationType": "装船",
//         "containerSize": "20",
//         "containerOneNumber": "MSDU9039106",
//         "containerTwoNumber": "MEDU9874519",
//         "movingDistance": "2330.0"
//     },
//     {
//         "truckNo": "T467",
//         "operationTime": "2024-06-04 09:37:03",
//         "position": "H102-250604",
//         "operationType": "卸船",
//         "containerSize": "20",
//         "containerOneNumber": "FCIU5392835",
//         "containerTwoNumber": "SNBU2261532",
//         "movingDistance": "2250.0"
//     },
//     {
//         "truckNo": "T468",
//         "operationTime": "2024-06-04 09:39:12",
//         "position": "H103-340482",
//         "operationType": "卸船",
//         "containerSize": "40",
//         "containerOneNumber": "UETU2778786",
//         "containerTwoNumber": "",
//         "movingDistance": "2220.0"
//     },
//     {
//         "truckNo": "T469",
//         "operationTime": "2024-06-04 09:41:20",
//         "position": "H103-340682",
//         "operationType": "卸船",
//         "containerSize": "40",
//         "containerOneNumber": "UETU2778786",
//         "containerTwoNumber": "",
//         "movingDistance": "2170.0"
//     },
//     {
//         "truckNo": "T469",
//         "operationTime": "2024-06-04 09:50:20",
//         "position": "340682-H103",
//         "operationType": "装船",
//         "containerSize": "40",
//         "containerOneNumber": "UQEU2745786",
//         "containerTwoNumber": "",
//         "movingDistance": "2270.0"
//     },
//     {
//         "truckNo": "T470",
//         "operationTime": "2024-06-04 09:56:29",
//         "position": "H103-340884",
//         "operationType": "卸船",
//         "containerSize": "40",
//         "containerOneNumber": "UETU2778786",
//         "containerTwoNumber": "",
//         "movingDistance": "1880.0"
//     }
// ]
type traceMoveProps = {
    truckTraceData:any,
    minHeight:any
}
const TrackMovingGraph: React.FC<traceMoveProps> = (props) => {

    const {truckTraceData,minHeight } = props;
    
    const arr = truckTraceData?.map((item:any) => ({
        truckNo: item?.truck_no,
        operationTime: item?.detail?.find((detail:any) => detail?.enname === 'operation_time')?.value,
        position: item?.detail?.find((detail:any) => detail?.enname === 'start_position_to_end_position')?.value,
        operationType:item?.detail?.find((detail:any)=>detail?.enname === 'operation_type')?.value,
        containerSize:item?.detail?.find((detail:any)=>detail?.enname === 'container_size')?.value,
        containerOneNumber:item?.detail?.find((detail:any)=>detail?.enname === 'container_1_number')?.value,
        containerTwoNumber:item?.detail?.find((detail:any)=>detail?.enname === 'container_2_number')?.value,
        movingDistance:item?.detail?.find((detail:any)=>detail?.enname=== 'moving_distance')?.value
    }));
    
    console.log('arr',arr);

    const updateOption = (data: any[]) => {

        let legendData: any = new Set();
        // xAixs: X轴数据：就是LOC1-LOC2位置轴
        let  xAixsBase: any = new Set();
        // yAxis：Y轴数据：就是时间轴
        let yAixsData: any = new Set();
        // seria:  数据部分：就是：tast_len
        const seriaBaseData = {};
    
    
        data.forEach( (td: any)=> {
    
            legendData.add(td.truckNo) // AGV编号
    
            yAixsData.add(td.operationTime) // 最终交箱时间
            xAixsBase.add(td.position)
    
            if (seriaBaseData[td.truckNo] === undefined) {
                seriaBaseData[td.truckNo] = [];
            }
    
            let symbolRotate= 0
            if ( td.operationType ==='LOAD'){symbolRotate=0 }
            if ( td.operationType ==='DSCH'){symbolRotate=-180 }
    
            let symbolSize=14
            if (td.containerSize ==='40'){symbolSize=20}

            seriaBaseData[td.truckNo].push(
                {
                    value: [
                                td.position,
                                td.operationTime, 
                                td.truckNo,
                                td.operationType,
                                td.containerSize,
                                td.containerOneNumber,
                                td.containerTwoNumber,
                                td.movingDistance
                    ],
                    symbol:"arrow",
                    symbolSize,
                    symbolRotate,
                    // itemStyle:{
                    //     opacity:1,
                    // }
                }
            )
        });
    
        legendData = Array.from(legendData)?.sort()
        xAixsBase = Array.from(xAixsBase)
        yAixsData = Array.from(yAixsData)?.sort()?.reverse()
    
        const dataSize: any[]=[]
        console.log('查看',seriaBaseData);
        
        Object.keys(seriaBaseData)?.forEach(key=>{
          seriaBaseData[key]=seriaBaseData[key]?.sort((a: any,b: any)=>new Date(a?.value[1])- new Date(b?.value[1]))
        })
    
        Object?.keys(seriaBaseData)?.forEach(key=>{
          dataSize?.push(seriaBaseData[key].length)
        })
    
        // console.log("seriaBaseData",seriaBaseData)
    
        let ds = 0;
        dataSize?.forEach( (item, index)=> {
            ds += item;
        });
        const ln: any[] = [];
        for (let i = 0; i <= ds; i+=1) {
            ln.push({
                source: i,
                target: i + 1,
                label:{
                    show:false
                }
            });
        }
        ln.pop();
    
        // seriadata
        const seriaData: any[] = [];
        const legendSelectedData={} // legend默认选择第一个AGV
    
        legendData?.forEach( (gtfAgoNo: any, index: any) =>{
            seriaData.push({
                name: gtfAgoNo,
                type: 'graph',
                layout: 'none',
                coordinateSystem: 'cartesian2d',
                legendHoverLink: true,
                symbol:'arrow',
                // symbol:seriaBaseData[ttfAgvNumber[0].value],
    
                edgeSymbol: ['none', 'arrow'],
                edgeSymbolSize: 6,
                data: seriaBaseData[gtfAgoNo],
                links: ln,
                animation: false,
                animationDelay:  (idx: any)=> {
                    return idx * 5;
                },
                itemStyle: {
                    normal: {
                        borderWidth: 1,
                        borderColor: '#333'
                    }
                },
                lineStyle:{
                    width:1,
                    color:'#edafda'
                }
            });
            legendSelectedData[gtfAgoNo]= index===0? true:false
        });
    
        xAixsBase.sort();
        const option = {
          title: {
              text: '集卡移动轨迹图',
              left: 'center',
             textStyle:{
                 color:'#333'
             }
          },
          legend: {
              data: legendData,
            //   right:"20%",
            //   left:"10%",
              top: 30,
              itemGap:5,
              width: '85%',
              type: 'scroll',
              itemWidth:10,
              itemHeight:10,
      
              selector:  [
                {
                    type: 'all',
                    title: '全选'
                },
                {
                    type: 'inverse',
                    title: '反选'
                }
              ],
              selectorItemGap:5,
              selectorPosition:"start",
              selectorLabel:{
                width:10,
                height:10
              },
              selected:legendSelectedData
          },
          toolbox: {
            show: true,
            feature: {
                restore: { // 重置
                    show: true
                },
                saveAsImage: {
                    show: true,
                    type:'jpeg',
                    backgroundColor:'white'
                }
            }
          },
          grid: [{
              x: 150,
              x2: 30,
              y2: 150,
            //   bottom:45,
              top:130
          }],
          dataZoom: [{
              type: 'slider',
              show: true,
              yAxisIndex: [0],
              filterMode: 'filter',
              left: '0%',
              start: 0,
              // end: YZoomLevel,
              end:100,
              handleIcon: 'path://path://M100, 100m -75, 0a75,75 0 1,0 150,0a75,75 0 1,0 -150,0',
          }, {
              type: 'inside',
              filterMode: 'filter',
              yAxisIndex: [0],
              start: 0,
              // end: YZoomLevel,
              end:100,
          },
              {
                  type: 'slider',
                  filterMode: 'filter',
                  xAxisIndex: [0],
                  start: 0,
                   // end: XZoomLevel,
                   end:100,
                  handleIcon: 'path://path://M100, 100m -75, 0a75,75 0 1,0 150,0a75,75 0 1,0 -150,0',
                  // slider的默认结束位置，该值可影响柱子的宽度
              }, {
                  type: 'inside',
                  filterMode: 'filter',
                  xAxisIndex: [0],
                  start: 0,
                  // end: XZoomLevel,
                  end:100,
                  // slider的默认结束位置，该值可影响柱子的宽度
              }],
          tooltip: {
              show: true,
              // position:'right',
              formatter:  (params: any, ticket: any, callback: any)=> {
                console.log('params',params);
                if(params.dataType == 'node'){
                    return `${params.seriesName}<br/>
                    起始-终止：${params?.data?.value?.[0]}<br/>
                    作业时间：${params?.data?.value?.[1]}<br/>
                    作业类型：${params?.data?.value?.[3]}<br/>
                    箱尺寸：${params?.data?.value?.[4]}<br/>
                    箱1箱号：${params?.data?.value?.[5]}<br/>
                    箱2箱号：${params?.data?.value?.[6]}<br/>
                    移动距离：${params?.data?.value?.[7]}<br/>
                    `
                }
              }
          },
          xAxis: {
              type: 'category',
              name: '起始-终止',
              nameLocation: 'middle',
              nameGap: 120,
              nameTextStyle: {
                  fontSize: 15,
                  color:'#333'
              },
              boundaryGap: false,
              data: xAixsBase,
    
              axisLine: {
                  show: true,
                  lineStyle: {
                      color: "#cccccc"
                  }
              },
    
              axisTick: {
                  show: false,
                  lineStyle: {
                      color: "#333"
                  }
              },
              axisLabel: {
                  show: true,
                  // interval: 0,
                  rotate: 60, // 控制x轴label角度
                  textStyle: {
                      color: "#999999"
                  }
              },
              splitLine: {
                  show: true,
                //   show: false,
                  lineStyle: {
                      color: ["#eeeeee"]
                  }
              },
              splitArea: {
                  show: false,
                  areaStyle: {
                      color: ["rgba(250,250,250,0.05)",
                          "rgba(200,200,200,0.02)"]
                  }
              }
          },
          yAxis: {
              type: 'category',
              name: '作业时间',
            //   nameLocation: 'middle',
            //   nameGap: 130,
              nameTextStyle: {
                  fontSize: 15,
                  color:'#333'
              },
              boundaryGap: false,
              data: yAixsData,
              axisLine: {
                  show: true,
                  lineStyle: {
                      color: "#cccccc"
                  }
              },
              axisTick: {
                  show: false,
                  lineStyle: {
                      color: "#333"
                  }
              },
              axisLabel: {
                  show: true,
                  textStyle: {
                      color: "#999999"
                  },
                  formatter:(value: any)=>{
                    // console.log('v',typeOf(value))
                    return value?.substr(0,19)
                  }
              },
              splitLine: {
                     show: true,
                //    show: false,
                  lineStyle: {
                      color: ["#eeeeee"]
                  }
              },
              splitArea: {
                  show: false,
                  areaStyle: {
                      color: ["rgba(250,250,250,0.05)",
                          "rgba(200,200,200,0.02)"]
                  }
              }
          },
          series: seriaData
        }
        return option;
      }

  return (
    <>
      <ReactEcharts
        theme={blueThemeName}
        lazyUpdate={true}
        notMerge={true}
        option={updateOption(arr)}
        //   onEvents={{ legendselectchanged: chartLegendselectchangedRef.current }}
        style={{ height: minHeight, width: '99%', margin: '0 auto' }}
      />
    </>
  );
};

export default TrackMovingGraph;
