
import { request } from 'umi';


// 修改拟真时间
export async function modifySimulationTimeNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/modify_simulation_time`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 拟真启动停止
export async function simulationStartAndStopNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/status`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 加减速
export async function simulationAccelerateNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/tm_speed`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 发箱启动停止
export async function simulationSendCntrStartAndStopNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/cntr_sender_status`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 实时CWP启动停止
export async function simulationRealtimeCWPStartAndStopNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/realtime_cwp_status`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 拟真重置
export async function simulationResetNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/reset`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 拟真初始化
export async function simulationDataIniNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/data_ini`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 切换码头
export async function changeDigitalTwinTermIdNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/switch_ct`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 获取默认参数
export async function getDefaultParameterNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/get_default_parameter`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 保存默认参数
export async function simulatioSaveParameterNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/save_parameter`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}

// 拟真单船小结
export async function queryVesselSummaryNew(options?: { [key: string]: any }) {
    return request<AscAutoAPI.ApiResult>(`/digitaltwinNew/simulation_control/hfs_simulation_summary`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    });
}
//TPS 下拉数据
export async function queryTpsData(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/TPS/GetTpsMainInfos`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
//TPS 详细数据
export async function fetchGenerateTpsEchartsData(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/TPS/GenerateTpsAnalysis`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
// TPS 参数界面 获取默认值参数 这个项目用不到
export async function queryDefaultParameter(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/tps_general_control/get_default_parameter`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
// TPS 参数界面 更新参数  这个项目用不到
export async function updateParameter(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/tps_general_control/update_parameter`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
//查询集卡作业分析小结接口  1.4
export async function queryTruckSummary(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/TPS/GetTrkSummary`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
//保存集卡作业分析小结接口   1.5
export async function saveTruckSummary(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/TPS/SaveTrkSummary`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
} 
// 查询集卡作业分析小结 下拉框时间数据接口   1.3
export async function queryTruckTimeOptions(options?: { [key: string]: any }) {
    return request<any>(`/tps_uiServer/TPS/GetTrkSummaryID`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
// 内集卡拖运量查询接口
export async function queryGetTransportVolume(options?:{[key:string]:any}){
    return request<any>(`/tps_truckQuery/tps_analysis/get_transport_volume`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
// 内集卡运行指标 查询接口
export async function queryGetTruckIndicator(options?:{[key:string]:any}){
    return request<any>(`/tps_truckQuery/tps_analysis/get_truck_indicator`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
// 内集卡运行指标 查询接口
export async function queryGetqueueAnalysis(options?:{[key:string]:any}){
    return request<any>(`/tps_truckQuery/tps_analysis/get_loadunload_queue_analysis`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}
export async function queryGetLoadunloadQueueAnalysis(options?:{[key:string]:any}){
    return request<any>(`/tps_truckQuery/tps_analysis/get_loadunload_queue_analysis_new`, {
        method: 'POST',
        data: options,
        ...(options || {}),
    }); 
}