declare namespace API {
  type ApiResult = {
    success?: boolean;
    code?: string;
    message?: string;
  };

  type ChangeUserPasswordCO = {
    id: number;
    oldPassword: string;
    newPassword: string;
  };

  type findUserByNameParams = {
    /** 用户登录名 */
    username: string;
  };

  type MenuCO = {
    /** 菜单标识 */
    id?: number;
    /** 菜单代码 */
    code?: string;
    /** 菜单名称 */
    name?: string;
    /** 路由路径 */
    routePath?: string;
    /** 菜单类型 */
    type?: string;
    /** 顺序 */
    sequence?: number;
    /** 子菜单 */
    children?: any;
  };

  type MenuUICO = {
    /** 权限标识 */
    permissionId?: number;
    /** 权限标识 */
    parenPermissionId?: number;
    /** 代码 */
    code?: string;
    /** 父代码 */
    parentCode?: string;
    /** 名称 */
    name?: string;
    /** 类型：0：菜单 1：菜单项目 2：UI项 */
    type?: number;
    /** 子菜单 */
    children?: any;
  };

  type MultiApiResultLong = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: number[];
    empty?: boolean;
    notEmpty?: boolean;
  };

  type MultiApiResultMenuCO = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: MenuCO[];
    empty?: boolean;
    notEmpty?: boolean;
  };

  type MultiApiResultMenuUICO = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: MenuUICO[];
    empty?: boolean;
    notEmpty?: boolean;
  };

  type MultiApiResultRoleCO = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: RoleCO[];
    empty?: boolean;
    notEmpty?: boolean;
  };

  type MultiApiResultString = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: string[];
    empty?: boolean;
    notEmpty?: boolean;
  };

  type PageCriteria = {
    current?: number;
    pageSize?: number;
  };

  type PagedApiResultPermissionUICO = {
    success?: boolean;
    code?: string;
    message?: string;
    total?: number;
    current?: number;
    pageSize?: number;
    data?: PermissionUICO[];
  };

  type PagedApiResultRoleCO = {
    success?: boolean;
    code?: string;
    message?: string;
    total?: number;
    current?: number;
    pageSize?: number;
    data?: RoleCO[];
  };

  type PagedApiResultUserQryCO = {
    success?: boolean;
    code?: string;
    message?: string;
    total?: number;
    current?: number;
    pageSize?: number;
    data?: UserQryCO[];
  };

  type PermissionUIAddCmd = {
    permissionUICO: PermissionUICO;
  };

  type PermissionUICO = {
    extValues?: Record<string, any>;
    /** 创建人 */
    creator?: string;
    /** 修改人 */
    modifier?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 标识 */
    id: number;
    /** 编码 */
    code?: string;
    /** 名称 */
    name?: string;
    /** 顺序 */
    sequence?: number;
    /** 菜单标识 */
    menuId?: number;
    /** permission_id */
    permissionId?: number;
  };

  type PermissionUIDelCmd = {
    permissionUIId: number;
    permissionId?: number;
  };

  type PermissionUIReOrderCmd = {
    ids?: number[];
  };

  type PermissionUIUpdateCmd = {
    permissionUICO: PermissionUICO;
  };

  type RoleAddCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户CO */
    roleCO: RoleCO;
  };

  type RoleBindPermissionCmd = {
    roleId: number;
    addedPermissionIds: number[];
    removedPermissionIds?: number[];
  };

  type RoleBoundPermissionQryCmd = {
    /** 角色id */
    roleId: number;
    /** 类型 */
    type?: string;
  };

  type RoleCO = {
    /** 角色Id */
    id?: number;
    /** 代码 */
    code: string;
    /** 角色名称 */
    name: string;
    /** 创建人 */
    creator?: string;
    /** 修改人 */
    modifier?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
  };

  type RoleGrantedMenuUITreeQry = {
    roleId?: number;
    granted?: boolean;
  };

  type RolesDeleteCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户Id */
    roleIds: number[];
  };

  type RoleUpdateCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户CO */
    roleCO: RoleCO;
  };

  type SimpleQueryRequest = {
    filter?: Record<string, any>;
    sorter?: Record<string, any>;
    pagination?: PageCriteria;
  };

  type SingleApiResultLong = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: number;
  };

  type SingleApiResultRoleCO = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: RoleCO;
  };

  type SingleApiResultUserCO = {
    success?: boolean;
    code?: string;
    message?: string;
    data?: UserCO;
  };

  type UserActiveCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户Id */
    userIds: number[];
  };

  type UserAddCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户CO */
    userAddCO: UserAddCO;
  };

  type UserAddCO = {
    extValues?: Record<string, any>;
    /** 用户登录名 */
    name: string;
    /** 用户名字 */
    fullName?: string;
    /** 用户头像URL */
    avatarUrl?: string;
    /** 邮箱 */
    email: string;
    /** 手机号码 */
    phoneNumber?: string;
    /** 是否可用 */
    enabled: boolean;
    /** 角色id */
    roleIds?: number[];
  };

  type UserChangePasswordCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 修改密码CO */
    changeUserPasswordCO: ChangeUserPasswordCO;
  };

  type UserCO = {
    extValues?: Record<string, any>;
    /** 用户id */
    id?: number;
    /** 用户登录名 */
    name: string;
    /** 用户名字 */
    fullName?: string;
    /** 用户头像URL */
    avatarUrl?: string;
    /** 邮箱 */
    email: string;
    /** 手机号码 */
    phoneNumber?: string;
    /** 是否可用 */
    enabled: boolean;
    /** 手机号 */
    mobilePhone?: string;
    /** 创建人 */
    creator?: string;
    /** 修改人 */
    modifier?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 最后一次登录时间 */
    lastLoginTime?: string;
    /** 登出时间 */
    lastLogoutTime?: string;
    /** 上次密码修改时间 */
    passwordModifyTime?: string;
    /** 角色ids */
    roleIds?: number[];
  };

  type UserDisableCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户Id */
    userIds: number[];
  };

  type UserGrantedMenuUITreeQry = {
    userId: number;
  };

  type UserQryCO = {
    extValues?: Record<string, any>;
    /** 用户id */
    id?: number;
    /** 用户登录名 */
    name: string;
    /** 用户名字 */
    fullName?: string;
    /** 用户头像URL */
    avatarUrl?: string;
    /** 邮箱 */
    email: string;
    /** 手机号码 */
    phoneNumber?: string;
    /** 是否可用 */
    enabled: boolean;
    /** 创建人 */
    creator?: string;
    /** 修改人 */
    modifier?: string;
    /** 创建时间 */
    createTime?: string;
    /** 最后一次修改时间 */
    updateTime?: string;
    /** 登录时间 */
    lastLoginTime?: string;
    /** 登出时间 */
    lastLogoutTime?: string;
    /** 角色ids */
    roleIds?: number[];
  };

  type UserResetPasswordCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 重置密码的用户id */
    userId: number;
  };

  type UserUpdateCmd = {
    operater?: string;
    needsOperator?: boolean;
    /** 用户CO */
    userUpdateCO: UserUpdateCO;
  };

  type UserUpdateCO = {
    extValues?: Record<string, any>;
    /** 用户id */
    id?: number;
    /** 用户登录名 */
    name: string;
    /** 用户名字 */
    fullName?: string;
    /** 用户头像URL */
    avatarUrl?: string;
    /** 邮箱 */
    email: string;
    /** 手机号码 */
    phoneNumber?: string;
    /** 是否可用 */
    enabled: boolean;
    /** 角色ids */
    roleIds?: number[];
  };

  type VerifyUserPasswordCmd = {
    /** id */
    id?: number;
    /** 密码 */
    password: string;
  };
}
