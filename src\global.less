html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.keep-alive-tabs {
  .ant-tabs-nav {
    margin: 0;
  }
}

.ant-pro-page-container-children-container {
  padding: 12px;
}
.ant-layout .ant-layout-footer{
  margin-block-start:0px;
  margin-block-end:0px;
}
.ant-pro-form-login-container {
  flex: none !important;
  margin-left: 10%;
}
.ant-pro-form-login-header{
  margin-top: 40px;
} 
.ant-pro-form-login-title {
  margin-left: 30px;
}
.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}
// 删除内容区距离padding值
.ant-pro-layout .ant-pro-layout-content{
  padding-block: 0 !important;
  padding-inline:10px !important;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}
