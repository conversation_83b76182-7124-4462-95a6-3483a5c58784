import React, { useRef } from 'react';
import * as echarts from 'echarts';
import ReactEcharts from 'echarts-for-react';
import EchartsThemebBlue, { blueThemeName } from '@/utils/EchartsThemebBlue';

echarts.registerTheme(blueThemeName, EchartsThemebBlue());

const XAxisData = [
  'H101',
  'H102',
  'H103',
  'H104',
  'H105',
  'H106',
  'H107',
  'H108',
  'H109',
  'H110',
  'H111',
  'H112',
  'H113',
  'H114',
];

type OEEChartProps = {
  truckOEEData:any,
  minHeight:any
}
const AgvEfficientOeeChart: React.FC<OEEChartProps> = (props) => {
  
  const { truckOEEData,minHeight } = props;
  const xAxisOEENumber =  Object?.keys(truckOEEData||{}); 
  
  const barValueList = xAxisOEENumber?.map((xNumber:any)=>{
    const sortedBarValues = truckOEEData[xNumber]
    .filter((item:any) => item.type === 'bar')
    .sort((a:any, b:any) => parseInt(a?.sequence) - parseInt(b?.sequence))
    .map((item:any) => item?.value);
    return sortedBarValues
  })
  const lineValueList = xAxisOEENumber?.map((xNumber:any)=>{
    const sortedLineValues = truckOEEData[xNumber]
          .filter((item:any)=>item.type === 'line')
          .map((item:any)=>item?.value)
    return sortedLineValues
  })
  const eachBarValueArr:any = [];
  for (let i = 0; i < barValueList?.[0]?.length; i++) {
      const temp = [];
      for (let j = 0; j < barValueList?.length; j++) {
          temp.push(barValueList[Number(j)][Number(i)]);
      }
      eachBarValueArr.push(temp);
  }

  const eachLineValueArr:any = [];
  for (let i = 0; i < lineValueList?.[0]?.length; i++) {
      const temp = [];
      for (let j = 0; j < lineValueList?.length; j++) {
          temp.push(lineValueList[j][i]);
      }
      eachLineValueArr.push(temp);
  }

const seriesName = truckOEEData[xAxisOEENumber?.[0]]?.filter((item:any)=>item?.type === 'bar');
const seriesLineName = truckOEEData[xAxisOEENumber?.[0]]?.filter((item:any)=>item?.type === 'line');


  const seriesList:any = [];
  seriesName?.forEach((item:any,index:number)=>{
    seriesList?.push({
      name: item?.cnname,
      type: item.type,
      data: eachBarValueArr[index],
      stack: '主全流程', //数据项所属的堆叠分组为主全流程
      yAxisIndex: 0,
      barMaxWidth: '60',
      label: {
        show: false,
        position: 'inside',
      },
    })
  })
  seriesLineName?.forEach((item:any,index:number)=>{
    console.log('item',item,eachLineValueArr[index]);
    if(item?.cnname?.includes('%')){
      seriesList?.push({
        name: item?.cnname,
        type: item.type,
        data: eachLineValueArr[index]?.map((item:any)=>parseFloat(item?.replace('%', ''))),
        yAxisIndex: index+1,
        smooth: false,
        label: { normal: { show: true, position: 'top', formatter:(params:any)=>{
          return `${params?.value}%`
        } },},
      })
    }else{
      seriesList?.push({
        name: item?.cnname,
        type: item.type,
        data: eachLineValueArr[index]?.map((item:any)=>Number(item)),
        yAxisIndex: index+1,
        smooth: false,
        label: { normal: { show: true, position: 'top', textStyle: {} } },
      })
    }
  })
  
  const yAxisArr:any = [
    {
      type: 'value',
      min: 0,
      name: '时间/分钟',
      nameTextStyle: {
        fontSize: 15,
        color:'#333'
      },
      axisLabel: {
        formatter: '{value}',
      },
      axisLine: {
        lineStyle: {
          color: '#C0C0C0',
        },
      },
    },
  ];
  seriesLineName?.forEach((item:any,index:number)=>{
    if(item?.cnname == 'UNIT数量'){
      yAxisArr.push(
        {
          type: 'value',
          // name: item?.cnname ,
          min: 0,
          nameTextStyle: {
            show:false,
            fontSize: 15,
          },
          axisLabel: {
            show:false,
            formatter: '{value}',
          },
          axisLine: {
            show:false,
            lineStyle: {
              color: '#C0C0C0',
            },
          },
          index: index+1,
          splitLine: {
            show: false,
          },
        },
      )
    }else{
      yAxisArr?.push({
        name: item?.cnname,
        type: 'value',
        min: 0,
        show: false,
        index: index+1,
      })
    }
  
  })
 
  const updateOption = () => {
    // 指定图表的配置项和数据
    const selectedData = {}; // 通过图例 控制 堆叠柱子和

    // 图例
    const lengendData: any[] = [];
    seriesList?.forEach((i:any) => {
      if (i.name !== '全流程') {
        lengendData?.push(i.name);
      }
    });
    lengendData?.forEach(i => {
      selectedData[i] = true;
    });

    const option = {
      title: {
        text: '集卡OEE分析',
        left: 'center',
        textStyle:{
          color:'#333'
      }
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
      },
      legend: {
        top: '5%',
        buttom: '120%',
        width: '85%',
        type: 'scroll',
        data: lengendData,
        selected: selectedData,
        selector: [
          {
            type: 'all',
            // 可以是任意你喜欢的 title
            title: '全选',
          },
          {
            type: 'inverse',
            // 可以是任意你喜欢的 title
            title: '反选',
          },
        ],
      },

      toolbox: {
        show: true,
        feature: {
            restore: { // 重置
                show: true
            },
          saveAsImage: { //保存图片
            show: true,
            type: 'jpeg',
            backgroundColor: 'white',
          },
        },
      },
      xAxis: [
        {
          type: 'category',
          name: '集卡编号',
          nameLocation: 'middle',
          nameGap: 25,
          nameTextStyle: {
            fontSize: 15,
            color:'#333'
          },
          data: xAxisOEENumber,
          axisLabel: {
            interval: 0,
            textStyle: {
              fontSize: 14,
            },
          },
          axisLine: {
            lineStyle: {
              color: '#C0C0C0',
            },
          },
        },
      ],
      yAxis:yAxisArr,
      grid: {
        // 控制图的大小，调整下面这些值就可以，
        x: 100,
        x2: 100,
        y: 150,
        y2: 90, // y2可以控制 X轴跟Zoom控件之间的间隔，避免以为倾斜后造成 label重叠到zoom上
      },

      dataZoom: [
        // 这个是控制Y轴的slider
        {
          type: 'slider',
          filterMode: 'empty',
          start: 0,
          end: 80,
          handleIcon: 'path://path://M100, 100m -75, 0a75,75 0 1,0 150,0a75,75 0 1,0 -150,0',
          // slider的默认结束位置，该值可影响柱子的宽度
        },
        // 这个是控制X轴的slider
        {
          type: 'slider',
          show: true,
          yAxisIndex: [0],
          filterMode: 'none',
          left: '2%',
          start: 0,
          end: 100,
          handleIcon: 'path://path://M100, 100m -75, 0a75,75 0 1,0 150,0a75,75 0 1,0 -150,0',
        },
      ],

      series: seriesList,
    };
    return option;
  };

  const chartLegendselectchanged = (params: any) => {
    console.log(
      '🚀 ~ file: OutTruckEfficiencyOeeChartSum.tsx ~ line 598 ~ chartLegendselectchanged ~ params',
      params,
    );
  };

  const chartLegendselectchangedRef = useRef(chartLegendselectchanged);

  return (
    <>
      <ReactEcharts
        theme={blueThemeName}
        lazyUpdate={true}
        notMerge={true}
        option={updateOption()}
        onEvents={{ legendselectchanged: chartLegendselectchangedRef.current }}
        style={{ height:minHeight, width: '99%', margin: '0 auto' }}
      />
    </>
  );
};

export default AgvEfficientOeeChart;
