// @ts-ignore
/* eslint-disable */

declare namespace AscAutoAPI {
  type ApiResult = {
    code?: string;
    data?: any;
    message?: string;
    success?: boolean;
  };

  type ParameterQC = {
    BayDistance?: number
    CabinDistance?: number
    ExpconperHour_Le?: number
    ExpconperHour_Lh?: number
    ExpconperHour_Sp?: number
    ExpconperHour_Ue?: number
    ExpconperHour_Uh?: number
    HatchCrossVessel?: number
    HatchDistance?: number
    Hugeconperhour_Sp?: number
    QCSafeDistance?: number
    QCSpeed?: number
    QCrossCabin?: number
    QCrossVessel?: number
    SpreaderHorizontalMoveAveSpeed_E?: number
    SpreaderHorizontalMoveAveSpeed_F?: number
    SpreaderHorizontalMoveSpeed_E_LB?: number
    SpreaderHorizontalMoveSpeed_E_UB?: number
    SpreaderHorizontalMoveSpeed_F_LB?: number
    SpreaderHorizontalMoveSpeed_F_UB?: number
    SpreaderOpenLockAveTime?: number
    SpreaderOpenLockTime_LB?: number
    SpreaderOpenLockTime_UB?: number
    SpreaderVerticalMoveAveSpeed_E?: number
    SpreaderVerticalMoveAveSpeed_F?: number
    SpreaderVerticalMoveSpeed_E_LB?: number
    SpreaderVerticalMoveSpeed_E_UB?: number
    SpreaderVerticalMoveSpeed_F_LB?: number
    SpreaderVerticalMoveSpeed_F_UB?: number
    TwinconRatio?: number
    UpdatedTime?: string
    expconper_HatchCover?: number
  };


  type ParameterYC = {
    BayScoreWeight?: number
    ExpconperHour_Le?: number
    ExpconperHour_Lh?: number
    ExpconperHour_Sp?: number
    ExpconperHour_Ue?: number
    ExpconperHour_Uh?: number
    Hugeconperhour_Sp?: number
    SpreaderHorizontalMoveAveSpeed_E?: number
    SpreaderHorizontalMoveAveSpeed_F?: number
    SpreaderHorizontalMoveSpeed_E_LB?: number
    SpreaderHorizontalMoveSpeed_E_UB?: number
    SpreaderHorizontalMoveSpeed_F_LB?: number
    SpreaderHorizontalMoveSpeed_F_UB?: number
    SpreaderOpenLockAveTime?: number
    SpreaderOpenLockTime_LB?: number
    SpreaderOpenLockTime_UB?: number
    SpreaderVerticalMoveAveSpeed_E?: number
    SpreaderVerticalMoveAveSpeed_F?: number
    SpreaderVerticalMoveSpeed_E_LB?: number
    SpreaderVerticalMoveSpeed_E_UB?: number
    SpreaderVerticalMoveSpeed_F_LB?: number
    SpreaderVerticalMoveSpeed_F_UB?: number
    TimeScoreWeight?: number
    TwinconRatio?: number
    UpdatedTime?: string
    YCSafeDistance?: number
    YCSpeed?: number
  };

  type ParameterTruck = {
    AdvancedTime?: number
    BatteryBurnRate?: number
    IGVHandingTime?: number
    IGVWaitingTime?: number
    OutputPerodiciTime?: number
    RoadDensity?: number
    RoutingPeriodicTime?: number
    TruckBatteryThreshold?: number
    TruckHighSpeedLaneAveSpeed_E?: number
    TruckHighSpeedLaneAveSpeed_F?: number
    TruckHighSpeedLaneSpeed_E_LB?: number
    TruckHighSpeedLaneSpeed_E_UB?: number
    TruckHighSpeedLaneSpeed_F_LB?: number
    TruckHighSpeedLaneSpeed_F_UB?: number
    TruckLength?: number
    TruckNum?: number
    TruckSafeDistance?: number
    TruckWorkLaneAveSpeed_E?: number
    TruckWorkLaneAveSpeed_F?: number
    TruckWorkLaneSpeed_LB_E?: number
    TruckWorkLaneSpeed_LB_F?: number
    TruckWorkLaneSpeed_UB_E?: number
    TruckWorkLaneSpeed_UB_F?: number
    UpdatedTime?: string
  };
}

