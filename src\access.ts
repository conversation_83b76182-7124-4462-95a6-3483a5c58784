/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(
  initialState:
    {
      currentUser?: API.CurrentUser | undefined;
      permissions?: string[] | undefined;
    }
    | undefined,
): Record<string, boolean | ((...param: any) => boolean)> {
  /*测试代码 支持刷新重新加载权限*/
  let { permissions } = initialState ?? {};

  function formatRoutePath(routePath: string) {
    const patten = /\/:(\w+)|\/\*/g;
    const path = routePath.replace(patten, '');
    const routeCode = path.replace(/^\//, '').split('/').join('_');
    return routeCode;
  }

  const canAccessMenu = (route: any) => {
    /* UMI4支持的路由配置
      /groups
      /groups/admin
      /users/:id        -> users
      /users
      /users/:id/messages
      /files/*         ->files
      /files/:id/*
    */
    const routeCode = formatRoutePath(route.path);
    if (undefined === permissions) {
      return false;
    }
    return permissions.findIndex(value => value === routeCode) >= 0;
  };

  const canAccessUI = (code: string) => {
    if (undefined === permissions) {
      return false;
    }
    return permissions.findIndex(value => value === code) >= 0;
  };

  return {
    canAccessMenu,
    canAccessUI,
  };
}
