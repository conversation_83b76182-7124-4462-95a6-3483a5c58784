/*
 * @Author: <PERSON><PERSON>deng
 * @version: 2.0
 * @Date: 2021-09-06 13:26:27
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-06-18 17:03:18
 * @Descripttion:
 */
import { ModalForm } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import React from 'react';
import ChangePasswordForm from './ChangePasswordForm';
//import ChangePasswordForm from './ChangePasswordForm';
import { useIntl } from 'umi';

export type ChangePasswordModalProps = {
  onVisibleChange: (flag: boolean) => void;
  onSubmit: (values: API.ChangeUserPasswordCO) => Promise<void>;
  changePasswordFormVisible: boolean;
};

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = (props) => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const intl = useIntl();
  const securityChangePassword = intl.formatMessage({
    id: 'securityChangePassword',
  });
  return (
    <ModalForm
      title={securityChangePassword}
      width="480px"
      visible={props.changePasswordFormVisible}
      onFinish={async (value) => {
        const changeUserPasswordCO = value as API.ChangeUserPasswordCO;
        changeUserPasswordCO.id = currentUser?.id ?? 0;
        props.onSubmit(changeUserPasswordCO);
      }}
      initialValues={{
        oldPassword: '',
        newPassword: '',
      }}
      onVisibleChange={props.onVisibleChange}
    >
      {<ChangePasswordForm />}
    </ModalForm>
  );
};

export default ChangePasswordModal;
