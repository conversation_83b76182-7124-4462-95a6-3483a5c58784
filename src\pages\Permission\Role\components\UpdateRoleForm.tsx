import { ModalForm, ProFormText } from '@ant-design/pro-form';
import React from 'react';
import {useIntl} from 'umi';

export type UpdateRoleFormProps = {
  onVisibleChange?: (visible: boolean) => void;
  onSubmit: (values: API.RoleCO) => Promise<void>;
  onCancel: (flag?: boolean, roleCO?: API.RoleCO) => void;
  updateRoleFormVisible: boolean;
  values: Partial<API.RoleCO>;
};

const UpdateRoleForm: React.FC<UpdateRoleFormProps> = (props) => {
  const intl = useIntl();
  const characterModifyCharacterInformation = intl.formatMessage({
    id: 'characterModifyCharacterInformation',
  });
  const characterCodeTwo = intl.formatMessage({
    id: 'characterCodeTwo',
  });
  const characterEnterCodeLessThan50Characters = intl.formatMessage({
    id: 'characterEnterCodeLessThan50Characters',
  });
  const characterName = intl.formatMessage({
    id: 'characterName',
  });
  const characterEnterNameLessThan50Characters = intl.formatMessage({
    id: 'characterEnterNameLessThan50Characters',
  });

  return (
    <ModalForm
      title={characterModifyCharacterInformation}
      width="400px"
      visible={props.updateRoleFormVisible}
      onFinish={props.onSubmit}
      onVisibleChange={props.onVisibleChange}
      initialValues={{
        code: props.values.code,
        name: props.values.name,
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormText
        name="code"
        label={characterCodeTwo}
        width="md"
        allowClear={true}
        rules={[
          {
            required: true,
            message: characterEnterCodeLessThan50Characters,
            max: 50,
          },
        ]}
      />
      <ProFormText
        name="name"
        label={characterName}
        width="md"
        allowClear={true}
        rules={[
          {
            required: true,
            message: characterEnterNameLessThan50Characters,
            max: 50,
          },
        ]}
      />
    </ModalForm>
  );
};

export default UpdateRoleForm;
