/*
 * @Descripttion:
 * @Author: miluo-刘书记
 * @Date: 2024-06-20 14:47:09
 * @LastEditors: sr
 * @LastEditTime: 2024-08-02 10:08:30
 */
import React, { useMemo } from 'react';

const iframeCSS: any = {
  borderRightWidth: '0px',
  borderLeftWidth: '0px',
  borderBottomWidth: '0px',
  position: 'relative',
  top: -40,
};

const TpsMonitor: React.FC = () => {

  const randomUUID = useMemo(() => {
    return Math.floor(Math.random() * 9000000000) + 1000000000;
  }, []);

  return (
    <>
      <div id="3D_div" style={{ height: 'calc(100vh - 145px)', position: 'relative' }}>
        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
          <iframe
            id="iframeDom"
            src={`${GRAFANA_PROXY.url}/d/e4fcbcf3-0d2e-44c9-90ff-15423cb845d9/business-monitor?orgId=1&fromIframe=true&token=${randomUUID}`} // &kiosk=tv是隐藏导航栏
            width="100%"
            height="100%"
            style={iframeCSS}
          ></iframe>
        </div>
      </div>
    </>
  );
};

export default TpsMonitor;
