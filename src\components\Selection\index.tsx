import { PlusOutlined } from '@ant-design/icons';
import { Divider, Input, Select, Space, Button,Table,Modal} from 'antd';
import type { InputRef } from 'antd';
import React, { useState, useRef } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import type { ColumnsType, ColumnType } from 'antd/es/table';
import type { FilterConfirmProps } from 'antd/es/table/interface';
// import Highlighter from 'react-highlight-words';
const Option = Select
let index = 0;

const SelectionCom: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef<InputRef>(null);
  const [open, setOpen] = useState(false);
  const flagCurrent = useRef(1)
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectRow,setSelectRow] = useState<any>('')
  const dataSource = [
    {
        key:'1',col1:'WGQ1',col2:'浦东',col3:'卸',
    },
    {
        key:'2',col1:'WGQ1',col2:'浦东',col3:'装',
    },
    {
        key:'3',col1:'WGQ2',col2:'振东',col3:'装',
    },
    {
        key:'4',col1:'WGQ2',col2:'振东',col3:'卸',
    },
    {
        key:'5',col1:'WGQ4',col2:'沪东',col3:'卸',
    },
    {
        key:'6',col1:'WGQ4',col2:'沪东',col3:'装',
    },
    {
        key:'7',col1:'WGQ5',col2:'明东',col3:'卸',
    },
    {
        key:'8',col1:'WGQ5',col2:'明东',col3:'装',
    },
  ]
  const handleSearch = (
    selectedKeys: string[],
    confirm: (param?: FilterConfirmProps) => void,
    dataIndex: any,
  ) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void) => {
    clearFilters();
    setSearchText('');
  };

  const getColumnSearchProps = (dataIndex: any): ColumnType<any> => {
    flagCurrent.current = 1
    return ({
       
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
          <div style={{ padding: 8 }} onKeyDown={e => e.stopPropagation()}>
            <Input
              ref={searchInput}
              placeholder={`Search ${dataIndex}`}
              value={`${selectedKeys[0] || ''}`}
              onChange={e => {
                e.preventDefault();
                setSelectedKeys(e.target.value ? [e.target.value] : [])
              }}
              onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
              style={{ marginBottom: 8, display: 'block' }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button
                onClick={() => clearFilters && handleReset(clearFilters)}
                size="small"
                style={{ width: 90 }}
              >
                Reset
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  confirm({ closeDropdown: false });
                  setSearchText((selectedKeys as string[])[0]);
                  setSearchedColumn(dataIndex);
                }}
              >
                Filter
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  close();
                }}
              >
                close
              </Button>
            </Space>
          </div>
        ),
        filterIcon: (filtered: boolean) => (
          <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
        ),
        onFilter: (value, record) =>
          record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()),
        onFilterDropdownOpenChange: visible => {
          if (visible) {
            setTimeout(() => searchInput.current?.select(), 100);
          }
        },
        // render: text
      })
  };

  const columns = [
    {
        title:'码头代码',
        dataIndex:'col1',
        key:'col1',
        ...getColumnSearchProps('col1'),
        sorter: (a, b) => a.col1.length - b.col1.length,
        sortDirections: ['descend', 'ascend'],
    },
    {
        title:'码头',
        dataIndex:'col2',
        key:'col2',
        ...getColumnSearchProps('col2'),
        sorter: (a, b) => a.col2.length - b.col2.length,
        sortDirections: ['descend', 'ascend'],
    },
    {
        title:'装/卸',
        dataIndex:'col3',
        key:'col3',
        ...getColumnSearchProps('col3'),
        sorter: (a, b) => a.col3.length - b.col3.length,
        sortDirections: ['descend', 'ascend'],
    },
  ]
  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectRow('')
  };
  return (
    <>
     <Select
      style={{ width: 200 }}
      placeholder="custom dropdown render"
      dropdownStyle={{
        width:0,
        height:0,
        padding:0
      }}
      open={open}
      onDropdownVisibleChange={(open)=>{
        setOpen(open)
        setIsModalOpen(true)
      }}
      value={selectRow}
      showSearch
      filterOption={(input, option) => (option?.label ?? '').includes(input)}  
    >
    </Select>
    <Modal open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
    <Table 
          columns={columns} 
          dataSource={dataSource} 
          size='small' 
          pagination={false}
          onChange={(pagination,filters,sorter)=>{
            console.log('shu',pagination,filters,sorter);
            
          }}
          onRow={(record: any) => ({
            onClick: () => {
                console.log(record);
                setSelectRow(`${record.col1}/${record.col2}/${record.col3}`)
                setIsModalOpen(false)
            },
          })}
          />
      </Modal>
    </>
   
  );
};

export default SelectionCom;