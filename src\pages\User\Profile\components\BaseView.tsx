import { findCurrentUser, updateUser } from '@/services/base/user';
import { UploadOutlined } from '@ant-design/icons';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { Button, message, Upload } from 'antd';
import type { RcFile, UploadChangeParam } from 'antd/lib/upload';
import dayjs from 'dayjs';
import React from 'react';
import { useIntl, useModel } from 'umi';
import styles from './BaseView.less';

const BaseView: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const intl = useIntl();

  const basicProfilePhotoFormat = intl.formatMessage({
    id: 'basicProfilePhoto',
  });
  const basicUpdateProfilePhotoFormat = intl.formatMessage({
    id: 'basicUpdateProfilePhoto',
  });
  const basicLoginIDFormat = intl.formatMessage({
    id: 'basicLoginID',
  });
  const basicFullNameFormat = intl.formatMessage({
    id: 'basicFullName',
  });
  const basicPhoneNumberFormat = intl.formatMessage({
    id: 'basicPhoneNumber',
  });
  const basicEmailFormat = intl.formatMessage({
    id: 'basicEmail',
  });
  const basicUploadImg = intl.formatMessage({
    id: 'basicUploadImg',
  });
  const basicImg2MB = intl.formatMessage({
    id: 'basicImg2MB',
  });
  const basicProfilePictureUploadedSuccessfully = intl.formatMessage({
    id: 'basicProfilePictureUploadedSuccessfully',
  });
  const basicInformationUpdatedSuccessfully = intl.formatMessage({
    id: 'basicInformationUpdatedSuccessfully',
  });
  const basicInformationUpdated = intl.formatMessage({
    id: 'basicInformationUpdated',
  });
  const basicPleaseEnterNameLessThan50Characters = intl.formatMessage({
    id: 'basicPleaseEnterNameLessThan50Characters',
  });
  const basicPhoneNumberFormatIncorrect = intl.formatMessage({
    id: 'basicPhoneNumberFormatIncorrect',
  });
  const basicEmailAddressFormatIsIncorrect = intl.formatMessage({
    id: 'basicEmailAddressFormatIsIncorrect',
  });

  const getAvatarURL = () => {
    if (currentUser && currentUser.avatarUrl) {
      const currentUnix = dayjs().unix();
      return `${currentUser.avatarUrl}?t=${currentUnix}`;
    }
    const url = 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png';
    return url;
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error(basicUploadImg);
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error(basicImg2MB);
    }
    return isJpgOrPng && isLt2M;
  };

  const handleChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const { response } = info.file;
      if (response.success === true) {
        message.success(basicProfilePictureUploadedSuccessfully);
      } else {
        message.error(response.message);
      }

      if (initialState?.currentUser) {
        initialState.currentUser.avatarUrl = response.data;
        setInitialState({ ...initialState });
      }
    }
  };

  const handleFinish = async (value: API.UserCO) => {
    const rest1 = await findCurrentUser();
    if (!rest1.success) {
      message.error(rest1.message);
      return false;
    }
    const latestUser = rest1.data;
    if (latestUser) {
      latestUser.fullName = value.fullName;
      latestUser.email = value.email;
      latestUser.phoneNumber = value.phoneNumber;
      const rest2 = await updateUser({ userUpdateCO: latestUser });
      if (rest2.success) {
        setInitialState({
          ...initialState,
          currentUser: latestUser,
        });
        message.success(basicInformationUpdatedSuccessfully);
      } else {
        message.error(rest2.message);
        return false;
      }
    }
    return true;
  };
  return (
    <div className={styles.baseView}>
      {
        <>
          <div className={styles.left}>
            <ProForm
              layout="vertical"
              onFinish={handleFinish}
              submitter={{
                resetButtonProps: {
                  style: {
                    display: 'none',
                  },
                },
                submitButtonProps: {
                  children: basicInformationUpdated,
                },
              }}
              initialValues={{
                ...currentUser,
              }}
              hideRequiredMark
            >
              <ProFormText
                name="name"
                label={basicLoginIDFormat}
                width="md"
                readonly={true}
                allowClear={false}
              />
              <ProFormText
                name="fullName"
                label={basicFullNameFormat}
                width="md"
                allowClear={true}
                rules={[
                  {
                    required: true,
                    message: basicPleaseEnterNameLessThan50Characters,
                    max: 50,
                  },
                ]}
              />
              <ProFormText
                width="md"
                name="phoneNumber"
                label={basicPhoneNumberFormat}
                rules={[
                  {
                    pattern: /^\d{11}$/,
                    message: basicPhoneNumberFormatIncorrect,
                  },
                ]}
              />
              <ProFormText
                width="md"
                name="email"
                label={basicEmailFormat}
                rules={[
                  {
                    type: 'email',
                    message: basicEmailAddressFormatIsIncorrect,
                  },
                ]}
              />
            </ProForm>
          </div>
        </>
      }
    </div>
  );
};

export default BaseView;
