pipeline {
    agent any
    environment {
        DOCKER_URL = 'registry.cn-shanghai.aliyuncs.com/gyzqbx'
        DOCKER_NAME = 'tps-monitor-ui'
     }
    parameters {
        string(name: 'CLIENT_NAME', defaultValue: 'miluo', description: '客户名称')
        string(name: 'ENV', defaultValue: 'prod', description: '环境')
        string(name: 'VERSION_PREFIX', defaultValue: '0.0', description: '版本号前缀')
    }
    stages {
        stage('Docker Build') {
            agent any
            steps {
                script {
                    def buildNumber = env.BUILD_NUMBER
                    version = "${VERSION_PREFIX}.${buildNumber}"
                    echo "版本: ${version}"

                    sh "docker build --build-arg ENV=${ENV} --tag ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version} ."
                    sh "docker push ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version}"

                    try {
                        sh "docker rmi -f ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version}"
                    } catch (err) {
                        echo 'clean none image error'
                    }
                }
            }
        }
    }
}
