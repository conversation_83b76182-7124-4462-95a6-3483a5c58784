// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量激活用户 批量激活用户信息 POST /v1/user/activeUserList */
export async function activeUserList(body: API.UserActiveCmd, options?: { [key: string]: any }) {
  return request<API.ApiResult>(`/base/v1/user/activeUserList`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加新用户 添加新用户 POST /v1/user/addUser */
export async function addUser(body: API.UserAddCmd, options?: { [key: string]: any }) {
  return request<API.SingleApiResultLong>(`/base/v1/user/addUser`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改用户密码 修改用户密码 POST /v1/user/changePassword */
export async function changePassword(
  body: API.UserChangePasswordCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/user/changePassword`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量禁用用户 批量禁用用户信息 POST /v1/user/disableUserList */
export async function disableUserList(body: API.UserDisableCmd, options?: { [key: string]: any }) {
  return request<API.ApiResult>(`/base/v1/user/disableUserList`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出用户信息 通过现有查询导出用户信息 POST /v1/user/exportUserList */
export async function exportUserList(
  body: API.SimpleQueryRequest,
  options?: { [key: string]: any },
) {
  return request<any>(`/base/v1/user/exportUserList`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取当前登录用户信息 根据当前用户token获取用户信息 POST /v1/user/findCurrentUser */
export async function findCurrentUser(options?: { [key: string]: any }) {
  return request<API.SingleApiResultUserCO>(`/base/v1/user/findCurrentUser`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 查询用户授权的界面权限码集合 查询用户授权的界面权限码集合 POST /v1/user/findCurrentUserGrantedUIPermissions */
export async function findCurrentUserGrantedUiPermissions(options?: { [key: string]: any }) {
  return request<API.MultiApiResultString>(`/base/v1/user/findCurrentUserGrantedUIPermissions`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取单个用户信息 根据用户登录名获取用户信息 GET /v1/user/findUserByName */
export async function findUserByName(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.findUserByNameParams,
  options?: { [key: string]: any },
) {
  return request<API.SingleApiResultUserCO>(`/base/v1/user/findUserByName`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询授权的界面权限 查询授权的界面权限 POST /v1/user/findUserGrantedMenuUITree */
export async function findUserGrantedMenuUiTree(
  body: API.UserGrantedMenuUITreeQry,
  options?: { [key: string]: any },
) {
  return request<API.MultiApiResultMenuUICO>(`/base/v1/user/findUserGrantedMenuUITree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户信息 根据通用查询获取用户信息 POST /v1/user/findUserPage */
export async function findUserPage(body: API.SimpleQueryRequest, options?: { [key: string]: any }) {
  return request<API.PagedApiResultUserQryCO>(`/base/v1/user/findUserPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 重置用户密码 重置用户密码 POST /v1/user/resetPassword */
export async function resetPassword(
  body: API.UserResetPasswordCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/user/resetPassword`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新用户 更新用户基本信息 POST /v1/user/updateUser */
export async function updateUser(body: API.UserUpdateCmd, options?: { [key: string]: any }) {
  return request<API.ApiResult>(`/base/v1/user/updateUser`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 当前密码校验 当前密码校验 POST /v1/user/verifyUserPassword */
export async function verifyUserPassword(
  body: API.VerifyUserPasswordCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/user/verifyUserPassword`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
