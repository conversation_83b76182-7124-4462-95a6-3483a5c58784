import {
  addRole,
  bindPermission,
  deleteRoleList,
  findRoleBoundPermissionSet,
  findRolePage,
  updateRole,
} from '@/services/base/role';
import { PlusOutlined } from '@ant-design/icons';
import { FooterToolbar } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useBoolean, useSetState } from 'ahooks';
import { Button, message, Popconfirm } from 'antd';
import React, { useRef, useState } from 'react';
import { FormattedMessage,useIntl } from 'umi';
import CreateRoleForm from './components/CreateRoleForm';
import RoleBindPermissionForm from './components/RoleBindPermissionForm';
import UpdateRoleForm from './components/UpdateRoleForm';

const Role: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [createRoleFormVisible, { set: setCreateRoleFormVisible }] = useBoolean(false);
  const [updateRoleFormVisible, { set: setUpdateRoleFormVisible }] = useBoolean(false);
  const [currentRoleBindedPermission, setCurrentRoleBindedPermission] = useState<string[]>([]);
  const [roleBindPermissionFormVisible, { set: setRoleBindPermissionFormVisible }] =
    useBoolean(false);
    useBoolean(false);
  const [currentRole, setCurrentRole] = useSetState<API.RoleCO>({ code: '', name: '' });
  const [selectedRoles, setSelectedRoles] = useState<API.RoleCO[]>([]);

  const cloneValues = JSON.parse(JSON.stringify(currentRoleBindedPermission));
  const intl = useIntl();

  const characterCodeFormat = intl.formatMessage({
    id: 'characterCode',
  });
  const characterNameFormat = intl.formatMessage({
    id: 'characterName',
  });
  const characterCreationTimeFormat = intl.formatMessage({
    id: 'characterCreationTime',
  });
  const characterModificationUserFormat = intl.formatMessage({
    id: 'characterModificationUser',
  });
  const characterModificationTimeFormat = intl.formatMessage({
    id: 'characterModificationTime',
  });
  const characterOptionsFormat = intl.formatMessage({
    id: 'characterOptions',
  });
  const characterBindingAuthorityFormat = intl.formatMessage({
    id: 'characterBindingAuthority',
  });
  const characterEditFormat = intl.formatMessage({
    id: 'characterEdit',
  });
  const characterDeleteFormat = intl.formatMessage({
    id: 'characterDelete',
  });
  const userAdditionFormat = intl.formatMessage({
    id: 'userAddition',
  });
  const userSelectedFormat = intl.formatMessage({
    id: 'userSelected',
  });
  const userItemFormat = intl.formatMessage({
    id: 'userItem',
  });
  const userGroupEnableUserGroupCancelFormat = intl.formatMessage({
    id: 'userGroupEnableUserGroupCancel',
  });
  const userGroupEnableUserGroupConfirmFormat = intl.formatMessage({
    id: 'userGroupEnableUserGroupConfirm',
  });
  const characterManagementFormat = intl.formatMessage({
    id: 'characterManagement',
  });
  const characterAddingCharacter = intl.formatMessage({
    id: 'characterAddingCharacter',
  });
  const characterAddCharacterSuccessful = intl.formatMessage({
    id: 'characterAddCharacterSuccessful',
  });
  const characterAddNewUserFailure = intl.formatMessage({
    id: 'characterAddNewUserFailure',
  });
  const characterModifyingCharacter = intl.formatMessage({
    id: 'characterModifyingCharacter',
  });
  const characterModifyCharacterSuccessful = intl.formatMessage({
    id: 'characterModifyCharacterSuccessful',
  });
  const characterModifyCharacterFailure = intl.formatMessage({
    id: 'characterModifyCharacterFailure',
  });
  const characterDeletingCharacter = intl.formatMessage({
    id: 'characterDeletingCharacter',
  });
  const characterDeletingCharacterSuccessful = intl.formatMessage({
    id: 'characterDeletingCharacterSuccessful',
  });
  const characterDeletingCharacterFailure = intl.formatMessage({
    id: 'characterDeletingCharacterFailure',
  });
  const characterConfirmDeleteCharacter = intl.formatMessage({
    id: 'characterConfirmDeleteCharacter',
  });
  const characterDeleteCharactersInBatches = intl.formatMessage({
    id: 'characterDeleteCharactersInBatches',
  });
  const characterBatchesDeleting = intl.formatMessage({
    id: 'characterBatchesDeleting',
  });
  const characterUnknownError = intl.formatMessage({
    id: 'characterUnknownError',
  });
  const characterSaveSuccessfully = intl.formatMessage({
    id: 'characterSaveSuccessfully',
  });

  const addRoleCmd = async (fields: API.RoleCO) => {
    const hide = message.loading(characterAddingCharacter);
    try {
      const result = await addRole({ roleCO: { ...fields } });
      hide();
      if (result.success) {
        message.success(characterAddCharacterSuccessful);
        return true;
      }
      message.error(result.message ?? '');
      return false;
    } catch (error) {
      hide();
      message.error(characterAddNewUserFailure);
      return false;
    }
  };

  const updateRoleCmd = async (fields: API.RoleCO) => {
    const hide = message.loading(characterModifyingCharacter);
    try {
      await updateRole({ roleCO: { ...currentRole, ...fields } });
      hide();
      message.success(characterModifyCharacterSuccessful);
      return true;
    } catch (error) {
      hide();
      message.error(characterModifyCharacterFailure);
      return false;
    }
  };

  const deleteRolesCmd = async (ids: number[]) => {
    const hide = message.loading(characterDeletingCharacter);
    try {
      const res = await deleteRoleList({ roleIds: ids });
      hide();
      if (res.success) {
        message.success(characterDeletingCharacterSuccessful);
        return true;
      }
      message.error(res.message);
      return false;
    } catch (error) {
      hide();
      message.error(characterDeletingCharacterFailure);
      return false;
    }
  };

  const columns: ProColumns<API.RoleCO>[] = [
    {
      title: characterCodeFormat,
      dataIndex: 'code',
      sorter: true,
    },
    {
      title: characterNameFormat,
      dataIndex: 'name',
      sorter: true,
    },
    {
      title: characterCreationTimeFormat,
      dataIndex: 'createTime',
      valueType: 'date',
      sorter: true,
    },
    {
      title: characterModificationUserFormat,
      dataIndex: 'modifier',
      sorter: true,
    },
    {
      title: characterModificationTimeFormat,
      dataIndex: 'updateTime',
      valueType: 'date',
      sorter: true,
    },
    {
      title: characterOptionsFormat,
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="bindPermission"
          onClick={async () => {
            const res = await findRoleBoundPermissionSet({ roleId: record.id || 0, type: 'UI' });
            if (!res || !res.success) return;
            setCurrentRoleBindedPermission(res.data || []);
            setCurrentRole(record);
            setRoleBindPermissionFormVisible(true);
          }}
        >
          {characterBindingAuthorityFormat}
        </a>,

        <a
          key="edit"
          onClick={() => {
            setCurrentRole(record);
            setUpdateRoleFormVisible(true);
          }}
        >
          {characterEditFormat}
        </a>,
        <Popconfirm
          key="pcDeleteRole"
          title={characterConfirmDeleteCharacter}
          onConfirm={async () => {
            const rest = await deleteRolesCmd([record.id ?? 0]);
            if (rest) {
              actionRef.current?.reloadAndRest?.();
            }
          }}
          okText={userGroupEnableUserGroupConfirmFormat}
          cancelText={userGroupEnableUserGroupCancelFormat}
        >
          <a key="delete">{characterDeleteFormat}</a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <>
      <ProTable<API.RoleCO>
        headerTitle={characterManagementFormat}
        actionRef={actionRef}
        rowKey="id"
        search={{ labelWidth: 120 }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setCreateRoleFormVisible(true);
            }}
          >
            <PlusOutlined />
            <FormattedMessage id="new" defaultMessage={userAdditionFormat} />
          </Button>,
        ]}
        columns={columns}
        request={(params, sorter) => {
          const { current, pageSize, ...query } = params;
          return findRolePage({
            filter: { ...query },
            pagination: { current: (current ?? 1) - 1, pageSize },
            sorter: { ...sorter },
          });
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRoles(selectedRows);
          },
        }}
        pagination={{ pageSize: 10 }}
      />

      {selectedRoles?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              {userSelectedFormat}
              <a style={{ fontWeight: 600 }}>{selectedRoles.length}</a> {userItemFormat}
            </div>
          }
        >
          <Popconfirm
            key="pcDeleteRoles"
            title={characterDeleteCharactersInBatches}
            onConfirm={async () => {
              const res = await deleteRolesCmd(selectedRoles.map((item) => item.id ?? 0));
              if (res) {
                setSelectedRoles([]);
                actionRef.current?.reloadAndRest?.();
              }
            }}
            okText={userGroupEnableUserGroupConfirmFormat}
            cancelText={userGroupEnableUserGroupCancelFormat}
          >
            <Button>{characterBatchesDeleting}</Button>
          </Popconfirm>
        </FooterToolbar>
      )}
      <CreateRoleForm
        onSubmit={async (value: API.RoleCO) => {
          const success = await addRoleCmd(value);
          if (success) {
            setCreateRoleFormVisible(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
        onVisibleChange={(visible: boolean) => {
          setCreateRoleFormVisible(visible);
        }}
        createRoleFormVisible={createRoleFormVisible}
        values={{}}
      />
      <UpdateRoleForm
        onSubmit={async (value: API.RoleCO) => {
          const success = await updateRoleCmd(value);
          if (success) {
            setUpdateRoleFormVisible(false);
            setCurrentRole(() => { });
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
        onCancel={() => {
          setUpdateRoleFormVisible(false);
          setCurrentRole(() => { });
        }}
        onVisibleChange={(visible: boolean) => {
          setUpdateRoleFormVisible(visible);
        }}
        updateRoleFormVisible={updateRoleFormVisible}
        values={currentRole || {}}
      />
      {roleBindPermissionFormVisible && (
        <RoleBindPermissionForm
          roleBindPermissionFormVisible={roleBindPermissionFormVisible}
          onCancel={() => {
            setRoleBindPermissionFormVisible(false);
            setCurrentRoleBindedPermission([]);
          }}
          onVisibleChange={(visible: boolean) => {
            setRoleBindPermissionFormVisible(visible);
          }}
          onSubmit={async (values: any) => {
            const addedList: any = [];
            const removedList: any = [];
            values.permissions?.forEach((permission: any) => {
              if (!cloneValues.includes(permission)) {
                addedList.push(permission);
              }
            });

            // 查找被删除的权限
            cloneValues?.forEach((permission: any) => {
              if (!values.permissions.includes(permission)) {
                removedList.push(permission);
              }
            });

            const res = await bindPermission({
              roleId: currentRole?.id || 0,
              addedPermissionIds: addedList,
              removedPermissionIds: removedList
            });
            if (!res || !res.success) {
              message.warning(`${res?.message || characterUnknownError}`);
              return;
            }
            message.success(characterSaveSuccessfully);
            setCurrentRoleBindedPermission([]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
            setRoleBindPermissionFormVisible(false);
          }}
          values={{
            permissions: [...currentRoleBindedPermission],
            roleId: currentRole?.id || 0,
            role: currentRole,
          }}
        />
      )}
    </>
  );
};

export default Role;
