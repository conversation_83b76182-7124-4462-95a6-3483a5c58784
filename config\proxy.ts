/*
 * @LastEditors: xiao-huahua
 * @Description:
 * @Author: sr
 * @Date: 2023-12-07 14:55:59
 */
/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  // 如果需要自定义本地开发服务器  请取消注释按需调整
  dev: {
    '/base': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^/base': '/tps-permission-service' },
    },
    '/auth/': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/tps_uiServer/': {
      target: 'http://**********:7088',
      changeOrigin: true,
      pathRewrite: { '^/tps_uiServer': '' },
    },
    '/tps_truckQuery/': {
      target: 'http://************:8890',//http://***********:8890
      changeOrigin: true,
      pathRewrite: { '^/tps_truckQuery': '' },
    },
  },

  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
