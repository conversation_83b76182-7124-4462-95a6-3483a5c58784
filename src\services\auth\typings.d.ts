declare namespace API {
  type approveOrDenyParams = {
    approvalParameters: Record<string, any>;
  };

  type checkToken2Params = {
    token: string;
  };

  type checkToken3Params = {
    token: string;
  };

  type checkToken5Params = {
    token: string;
  };

  type checkToken6Params = {
    token: string;
  };

  type checkTokenParams = {
    token: string;
  };

  type getAccessTokenParams = {
    parameters: Record<string, any>;
  };

  type OAuth2AccessToken = {
    value?: string;
    expired?: boolean;
    tokenType?: string;
    additionalInformation?: Record<string, any>;
    refreshToken?: OAuth2RefreshToken;
    expiresIn?: number;
    scope?: string[];
    expiration?: string;
  };

  type OAuth2RefreshToken = {
    value?: string;
  };

  type postAccessTokenParams = {
    parameters: Record<string, any>;
  };

  type View = {
    contentType?: string;
  };
}
