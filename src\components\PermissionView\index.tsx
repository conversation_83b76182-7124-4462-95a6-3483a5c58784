import { MenuUnfoldOutlined, ProfileOutlined, TableOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { Tree } from 'antd';
import { BasicDataNode, DataNode } from 'antd/es/tree';
import React, { useEffect, useState } from 'react';

type PermissionViewProps = {
  modalOpen: boolean;
  handleModalOpen?: (modalOpen: boolean) => void;
  grantedMenuUITreeData: API.MenuUICO[];
};

const treeIcons: React.ReactElement[] = [
  // eslint-disable-next-line react/jsx-key
  <MenuUnfoldOutlined />,
  // eslint-disable-next-line react/jsx-key
  <ProfileOutlined />,
  // eslint-disable-next-line react/jsx-key
  <TableOutlined />,
];

const PermissionView: React.FC<PermissionViewProps> = (props) => {
  const [treeData, setTreeData] = useState<(BasicDataNode | DataNode)[]>([]);

  const getItem = (
    key: string,
    type: number,
    title: string,
    children?: any[],
  ): BasicDataNode | DataNode => {
    if (children?.length === 0) {
      return {
        icon: treeIcons[type],
        title,
        key,
      };
    }

    return {
      icon: treeIcons[type],
      title,
      key,
      children,
    };
  };

  const convertToTreeNodes = (menuUITreeData: API.MenuUICO[]): (BasicDataNode | DataNode)[] => {
    return menuUITreeData.map((item) => {
      return getItem(
        `${item.type}-${item.code}-${item.permissionId}`,
        item.type ?? 0,
        item.name ?? '',
        item.children ? convertToTreeNodes(item.children) : [],
      );
    });
  };

  // useMount(() => {
  //   const treeNodes = convertToTreeNodes(props.grantedMenuUITreeData);
  //   setTreeData(treeNodes);
  // });

  useEffect(() => {
    const treeNodes = convertToTreeNodes(props.grantedMenuUITreeData);
    setTreeData(treeNodes);
  }, [props.grantedMenuUITreeData]);

  return (
    <ModalForm
      title={`界面权限预览`}
      width="420px"
      style={{ padding: '12px 12px 12px' }}
      open={props.modalOpen}
      onOpenChange={props.handleModalOpen}
      onFinish={async () => {
        props?.handleModalOpen?.(false);
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <Tree showLine={true} showIcon={true} defaultExpandAll={true} treeData={treeData} />
    </ModalForm>
  );
};

export default PermissionView;
