import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import { GridContent } from '@ant-design/pro-layout';
import { Menu } from 'antd';
import React, { useLayoutEffect, useRef, useState } from 'react';
import { useIntl } from 'umi';
import BaseView from './components/BaseView';
import SecurityView from './components/SecurityView';
import styles from './style.less';

const { Item } = Menu;

type SettingsStateKeys = 'base' | 'security';
type SettingsState = {
  mode: 'inline' | 'horizontal';
  selectKey: SettingsStateKeys;
};

const Profile: React.FC = () => {
  const intl = useIntl();
  const basicSettingFormat = intl.formatMessage({
    id: 'basicSetting',
  });
  const securitySettingFormat = intl.formatMessage({
    id: 'securitySetting',
  });
  const menuMap: Record<string, React.ReactNode> = {
    base: basicSettingFormat,
    security: securitySettingFormat,
  };

  const [initConfig, setInitConfig] = useState<SettingsState>({
    mode: 'inline',
    selectKey: 'base',
  });
  const dom = useRef<HTMLDivElement>();

  const resize = () => {
    requestAnimationFrame(() => {
      if (!dom.current) {
        return;
      }
      let mode: 'inline' | 'horizontal' = 'inline';
      const { offsetWidth } = dom.current;
      if (dom.current.offsetWidth < 641 && offsetWidth > 400) {
        mode = 'horizontal';
      }
      if (window.innerWidth < 768 && offsetWidth > 400) {
        mode = 'horizontal';
      }
      setInitConfig({ ...initConfig, mode: mode as SettingsState['mode'] });
    });
  };

  useLayoutEffect(() => {
    if (dom.current) {
      window.addEventListener('resize', resize);
      resize();
    }
    return () => {
      window.removeEventListener('resize', resize);
    };
  }, [dom.current]);

  const getMenu = () => {
    return Object.keys(menuMap).map((item) => <Item key={item}>{menuMap[item]}</Item>);
  };

  const renderChildren = () => {
    const { selectKey } = initConfig;
    switch (selectKey) {
      case 'base':
        return <BaseView />;
      case 'security':
        return <SecurityView />;
      default:
        return null;
    }
  };

  return (
    <PageContainer
      header={{
        title: null,
        breadcrumb: {},
      }}
    >
      <GridContent>
        <div
          className={styles.main}
          ref={(ref) => {
            if (ref) {
              dom.current = ref;
            }
          }}
        >
          <div className={styles.leftMenu}>
            <Menu
              mode={initConfig.mode}
              selectedKeys={[initConfig.selectKey]}
              onClick={({ key }) => {
                setInitConfig({
                  ...initConfig,
                  selectKey: key as SettingsStateKeys,
                });
              }}
            >
              {getMenu()}
            </Menu>
          </div>
          <div className={styles.right}>
            <div className={styles.title}>{menuMap[initConfig.selectKey]}</div>
            {renderChildren()}
          </div>
        </div>
      </GridContent>
    </PageContainer>
  );
};

export default Profile;
