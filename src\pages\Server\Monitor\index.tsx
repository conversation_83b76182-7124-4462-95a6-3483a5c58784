import React, { useMemo } from 'react';

const iframeCSS: any = {
  borderRightWidth: '0px',
  borderLeftWidth: '0px',
  borderBottomWidth: '0px',
  position: 'relative',
  top: -40,
};

const MonitorServer: React.FC = () => {

  const randomUUID = useMemo(() => {
    return Math.floor(Math.random() * 9000000000) + 1000000000;
  }, []);

  return (
    <>
      <div id="3D_div" style={{ height: 'calc(100vh - 145px)', position: 'relative' }}>
        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
          <iframe
            id="iframeDom"
            src={`${GRAFANA_PROXY.url}/d/9CWBzd1f0bik001/linux?orgId=1&fromIframe=true&token=${randomUUID}`} // &kiosk=tv是隐藏导航栏
            width="100%"
            height="100%"
            style={iframeCSS}
          ></iframe>
        </div>
      </div>
    </>
  );
};

export default MonitorServer;
