﻿import type { RequestOptions } from '@@/plugin-request/request';
import type { RequestConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { message } from 'antd';
const loginPath = '/user/login';

// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}
// 与后端约定的响应数据格式
interface ResponseStructure {
  success: boolean;
  data: any;
  code?: number;
  message?: string;
  // showType?: ErrorShowType;   这个不删除，errorThrower 捕获到错误 界面就崩掉了，因为 res 不符合 ResponseStructure 格式定义
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res: any) => {
      const { success, data, code, message } = res as ResponseStructure;
      if (success === false) {  // 如果后端 success 为 false，自定义错误
        const error: any = new Error(message || '未知错误');
        error.name = 'BizError';
        error.info = { code, message: message || '未知错误', data };
        throw error;
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      if (error.name === 'BizError') {  // 处理自定义的错误
        const { code, message:errorMessage } = error.info;
        message.error(`错误 ${code}:${errorMessage}`); // 这里的code 和 errorMessage  就是后端接口返回的 code 和 message
      } else if (error.name === 'AxiosError') {   // 这里是处理请axios相关的错误，因为umimax用的是 axios
        if (error.response) {
          if (error.response.status === 401) {
            history.push(loginPath);
            message.error(`认证失败: ${error.response.data?.error + '请重新登录'}`); // error.response.data?.error：invalid_token
          } else {
            message.error(`请求错误: ${error.response.status} - ${error.response.data?.msg || error.errorMessage}`);
          }
        } else if (error.request) {
          message.error('网络错误，请检查您的网络连接');
        } else {
          message.error('发送请求时出错');
        }
      } else {
        if (error.message?.includes('cancelToken')) {
          message.error('token被清除,请求已取消,请重新登录');
        } else {
          message.error('发生未知错误，请重试');
        }
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (options: RequestOptions) => {
      // 拦截请求配置，进行个性化处理。
      const copyOptions: any = options;
      if (!(copyOptions.headers && copyOptions.headers.Authorization)) {
        // 检查本地存储是否有 token
        const token = localStorage.getItem('token');
        if (!token) {
          history.push(loginPath);
          return
        }
        copyOptions.headers = {
          ...options.headers,
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        };
      }
      return {
        skipErrorHandler: true,
        ...copyOptions,
      };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response as unknown as ResponseStructure;
      if (data?.success === false) {
        // message.error(data?.message);
      }
      return response;
    },
  ],
};
