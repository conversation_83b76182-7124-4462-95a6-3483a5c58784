
import { SearchOutlined } from '@ant-design/icons';
import { Button, Input, Space, Table } from 'antd';
import {
    Card,
    Row,
    Col,
    Form,
    DatePicker,
    Select,
    message,
  } from 'antd';
import type { ColumnsType, ColumnType } from 'antd/es/table';
import type { FilterConfirmProps } from 'antd/es/table/interface';
import React, { useEffect, useRef, useState } from 'react';
import Highlighter from 'react-highlight-words';
import useExcelExport from '@/hooks/useExcelExport';
import {queryTruckSummary,saveTruckSummary,queryTruckTimeOptions} from '@/services/ascAutoDigitalwin/api'
import moment from 'moment';

interface DataType {
  intruck_id:string;
  intruck_kpi_cycle: string;
  intruck_kpi_move: string,
  intruck_kpi_move_per_h:string,
  intruck_kpi_move_yv: string,
  intruck_kpi_move_vy: string,
  intruck_kpi_move_yy: string,
  intruck_kpi_move_heavy: string,
  intruck_kpi_move_heavy_r: string,
  intruck_kpi_two_cntr: string,
  intruck_kpi_two_cntr_r: string,
  intruck_kpi_dis: string
}

const cnTitle = {
    intruck_id:'集卡编号',
    intruck_kpi_cycle: '周转时间(min)',
    intruck_kpi_move: '总任务数',
    intruck_kpi_move_per_h:'每小时任务数',
    intruck_kpi_move_yv: '装船任务数',
    intruck_kpi_move_vy: '卸船任务数',
    intruck_kpi_move_yy: '堆场转堆任务数',
    intruck_kpi_move_heavy: '重进重出任务数',
    intruck_kpi_move_heavy_r: '重进重出比例',
    intruck_kpi_two_cntr: '双箱任务数',
    intruck_kpi_two_cntr_r: '双箱率',
    intruck_kpi_dis: '行驶距离'
}
const obj_2:any = {
    intruck_kpi_cycle_2: null,
    intruck_kpi_move_2: null,
    intruck_kpi_move_per_h_2:null,
    intruck_kpi_move_yv_2: null,
    intruck_kpi_move_vy_2: null,
    intruck_kpi_move_yy_2: null,
    intruck_kpi_move_heavy_2: null,
    intruck_kpi_move_heavy_r_2: null,
    intruck_kpi_two_cntr_2: null,
    intruck_kpi_two_cntr_r_2: null,
    intruck_kpi_dis_2: null
}
type DataIndex = keyof DataType;

type TrackJobAnalysisProps = {
    truckSummaryData:any;
    minHeight:any;
    rangeTime:any;
    force:number
}

const TrackJobAnalysis:React.FC<TrackJobAnalysisProps> = (props)=>{

      const {truckSummaryData,minHeight,rangeTime,force} = props;
    const [inputForm] = Form.useForm()
    const [selectForm] = Form.useForm()
    
    // 数据1 数据2 下拉框数据
    const [timeOptions,setTimeOptions] = useState<any>()
    // 表格当前数据
    const [currentTimeData,setCurrentTimeData] = useState<any>([])
    const [cloneCurrentTimeData,setCloneCurrentTimeData] = useState<any>([])
    // 当前合计
    const [currentSummary,setCurrentSummary] = useState<any>({})
    // 集卡号过滤下拉数据
    const [truckOptions,setTruckOptions] = useState<any>([])
    // 所有数据
    const [allDataSource,setAllDataSource] = useState<any>()
    const [duibiAllData,setDuibiAllData] = useState<any>()
    // 点了对比后，禁用保存功能
    const [disableedFlag,setDisabledFlag] = useState<boolean>(false)
    // 选历史数据也无法保存
    const [selectHistoryData,setSelectHistoryData] = useState<boolean>(false)
    // 过滤框记录上次打开的列
    const [lastOpenedColumn, setLastOpenedColumn] = useState<any>(null);

    const exportToExcel = useExcelExport();

    const getColumnSearchProps = (dataIndex: DataIndex):any => {
        const frontValues = cloneCurrentTimeData.map((item:any) => item[dataIndex]);
        const backValues = cloneCurrentTimeData.map((item:any) =>item[dataIndex]);
        const minCycle = Math.min(...frontValues,...backValues);
        const maxCycle = Math.max(...frontValues,...backValues);
        return  {
            filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
              <div style={{ padding: 8 }} onKeyDown={e => e.stopPropagation()}>
                <div style={{display:'flex',}}>
                  <Form size='small' form={inputForm}  layout="inline">
                      <Form.Item name='minNumber'>
                         <Input style={{width:100}} ></Input>
                      </Form.Item>
                      <Form.Item>~</Form.Item>
                      <Form.Item name='maxNumber'>
                         <Input placeholder='' style={{width:100}}></Input>
                      </Form.Item>
                  </Form>
                </div>
                <div style={{display:'flex',justifyContent:'space-between'}}>
                  <span>最小值:{isFinite(minCycle) ? minCycle : undefined}</span>
                  <span>最大值:{isFinite(maxCycle) ? maxCycle : undefined}</span>
                </div>
                <Space>
                  <Button
                    type="primary"
                    onClick={() => {
                      const str = inputForm?.getFieldsValue()
                      console.log('过滤条件',str,dataIndex,currentTimeData);
                      if(disableedFlag){
                        const filterRangeData = currentTimeData.filter((item:any)=>{
                            const min = item[dataIndex] 
                            const min_2 = item[`${dataIndex}_2`]
                            if(min <= Number(str?.maxNumber) && min >= Number(str.minNumber) || min_2 <= Number(str?.maxNumber) && min_2 >= Number(str.minNumber)){
                                return item
                            }
                           })
                           setCurrentTimeData(filterRangeData)
                      }else{
                        const filterRangeData = currentTimeData.filter((item:any)=>{
                            const min = item[dataIndex] 
                            if(min <= Number(str?.maxNumber) && min >= Number(str.minNumber)){
                                return item
                            }
                           })
                           setCurrentTimeData(filterRangeData)
                      }
                  }}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90 }}
                  >
                    过滤
                  </Button>
                  <Button
                    onClick={() =>{
                      inputForm.setFieldsValue({
                          minNumber:'',
                          maxNumber:''
                        });
                        if(disableedFlag){
                         setCurrentTimeData(duibiAllData)
                        }else{
                            setCurrentTimeData(cloneCurrentTimeData)
                        }
                    }}
                    size="small"
                    style={{ width: 90 }}
                  >
                    重置
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      close();
                    }}
                  >
                    关闭
                  </Button>
                </Space>
              </div>
            ),
            onFilterDropdownOpenChange:(visible:any)=>{
                console.log('打开了',visible,dataIndex);
                if(visible){
                    if(lastOpenedColumn !== dataIndex){ // 打开的不是同一列，清空过滤条件及表格数据
                        inputForm.setFieldsValue({
                            minNumber:'',
                            maxNumber:''
                        });
                        setLastOpenedColumn(dataIndex);
                        if(disableedFlag){
                            setCurrentTimeData(duibiAllData)
                        }else{
                            setCurrentTimeData(cloneCurrentTimeData)
                        }
                    }
                }
           
            }
          }
    };
  
    const columns_2: ColumnsType<any> = [
        {
            title: '集卡编号',
            dataIndex: 'intruck_id',
            key: 'intruck_id',
            width: 80,
            filters: truckOptions || [],
            filterSearch: true,
            onFilter: (value: any, record) => record?.intruck_id?.startsWith(value),
        },
        {
            title: '周转时间(min)',
             ...getColumnSearchProps('intruck_kpi_cycle'),
            children:[
               {
                title:'(1)',
                dataIndex: 'intruck_kpi_cycle',
                width: 60,
                key: 'intruck_kpi_cycle',
                sorter: (a, b) => {
                const aValue = a?.intruck_kpi_cycle;
                const bValue = b?.intruck_kpi_cycle;
                return aValue - bValue
            },
            sortDirections: ['descend', 'ascend'],
               },
               {
                title:'(2)',
                dataIndex: 'intruck_kpi_cycle_2',
                width: 60,
                key: 'intruck_kpi_cycle_2',
                sorter: (a, b) => {
                    const aValue = a?.intruck_kpi_cycle_2;
                    const bValue = b?.intruck_kpi_cycle_2;
                    return aValue - bValue
                },
                sortDirections: ['descend', 'ascend'],
               }
            ],
        },
        {
           
            title: '总任务数',
            ...getColumnSearchProps('intruck_kpi_move'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move',
                    key: 'intruck_kpi_move',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move;
                        const bValue = b?.intruck_kpi_move;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_2',
                    key: 'intruck_kpi_move_2',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_2;
                        const bValue = b?.intruck_kpi_move_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                }
            ],
           
        },
        {
            title: '每小时任务数',
            ...getColumnSearchProps('intruck_kpi_move_per_h'),
            children:[
                {  
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_per_h',
                    key: 'intruck_kpi_move_per_h',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_per_h;
                        const bValue = b?.intruck_kpi_move_per_h;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {  
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_per_h_2',
                    key: 'intruck_kpi_move_per_h_2',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_per_h_2;
                        const bValue = b?.intruck_kpi_move_per_h_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
        },
        {
       
            title: '装船任务数',
            ...getColumnSearchProps('intruck_kpi_move_yv'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_yv',
                    width: 60,
                    key: 'intruck_kpi_move_yv',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_yv;
                        const bValue = b?.intruck_kpi_move_yv;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_yv_2',
                    width: 60,
                    key: 'intruck_kpi_move_yv_2',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_yv_2;
                        const bValue = b?.intruck_kpi_move_yv_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ],
        },
        {
          
            title: '卸船任务数',
            ...getColumnSearchProps('intruck_kpi_move_vy'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_vy',
                    width: 55,
                    key: 'intruck_kpi_move_vy',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_vy;
                        const bValue = b?.intruck_kpi_move_vy;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_vy_2',
                    width: 55,
                    key: 'intruck_kpi_move_vy_2',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_vy_2;
                        const bValue = b?.intruck_kpi_move_vy_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
           
        },
        {
            title: '堆场转堆任务数',
            ...getColumnSearchProps('intruck_kpi_move_yy'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_yy',
                    width: 65,
                    key: 'intruck_kpi_move_yy',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_yy;
                        const bValue = b?.intruck_kpi_move_yy;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_yy_2',
                    width: 65,
                    key: 'intruck_kpi_move_yy_2',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_yy_2;
                        const bValue = b?.intruck_kpi_move_yy_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                }
            ]
          
        },
        {
         
            title: '重进重出任务数',
            ...getColumnSearchProps('intruck_kpi_move_heavy'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_heavy',
                    key: 'intruck_kpi_move_heavy',
                    width: 65,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_heavy;
                        const bValue = b?.intruck_kpi_move_heavy;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_heavy_2',
                    key: 'intruck_kpi_move_heavy_2',
                    width: 65,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_heavy_2;
                        const bValue = b?.intruck_kpi_move_heavy_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                }
            ]
        },
        {
            title: '重进重出比例',
            ...getColumnSearchProps('intruck_kpi_move_heavy_r'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_heavy_r',
                    key: 'intruck_kpi_move_heavy_r',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_heavy_r;
                        const bValue = b?.intruck_kpi_move_heavy_r;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_move_heavy_r_2',
                    key: 'intruck_kpi_move_heavy_r_2',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_heavy_r_2;
                        const bValue = b?.intruck_kpi_move_heavy_r_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                }
            ]
           
        },
        {
        
            title: '双箱任务数',
            ...getColumnSearchProps('intruck_kpi_two_cntr'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_two_cntr',
                    key: 'intruck_kpi_two_cntr',
                    width: 55,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_two_cntr;
                        const bValue = b?.intruck_kpi_two_cntr;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_two_cntr_2',
                    key: 'intruck_kpi_two_cntr_2',
                    width: 55,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_two_cntr_2;
                        const bValue = b?.intruck_kpi_two_cntr_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                }
            ]
           
        },
        {
       
            title: '双箱率',
            ...getColumnSearchProps('intruck_kpi_two_cntr_r'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_two_cntr_r',
                    key: 'intruck_kpi_two_cntr_r',
                    width: 45,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_two_cntr_r;
                        const bValue = b?.intruck_kpi_two_cntr_r;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_two_cntr_r_2',
                    key: 'intruck_kpi_two_cntr_r_2',
                    width: 45,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_two_cntr_r_2;
                        const bValue = b?.intruck_kpi_two_cntr_r_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
        },
        {
            title: '行驶距离',
            ...getColumnSearchProps('intruck_kpi_dis'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_dis',
                    key: 'intruck_kpi_dis',
                    width: 50,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_dis;
                        const bValue = b?.intruck_kpi_dis;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
                {
                    title:'(2)',
                    dataIndex: 'intruck_kpi_dis_2',
                    key: 'intruck_kpi_dis_2',
                    width: 50,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_dis_2;
                        const bValue = b?.intruck_kpi_dis_2;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
          
        },
    ];
    const columns: ColumnsType<any> = [
        {
            title: '集卡编号',
            dataIndex: 'intruck_id',
            key: 'intruck_id',
            width: 80,
            filters: truckOptions || [],
            filterSearch: true,
            onFilter: (value: any, record) => record?.intruck_id?.startsWith(value),
        },
        {
            title: '周转时间(min)',
             ...getColumnSearchProps('intruck_kpi_cycle'),
            children:[
               {
                title:'(1)',
                dataIndex: 'intruck_kpi_cycle',
                width: 60,
                key: 'intruck_kpi_cycle',
                sorter: (a, b) => {
                const aValue = a?.intruck_kpi_cycle;
                const bValue = b?.intruck_kpi_cycle;
                return aValue - bValue
            },
            sortDirections: ['descend', 'ascend'],
               },
            ],
        },
        {
           
            title: '总任务数',
            ...getColumnSearchProps('intruck_kpi_move'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move',
                    key: 'intruck_kpi_move',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move;
                        const bValue = b?.intruck_kpi_move;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ],
           
        },
        {
            title: '每小时任务数',
            ...getColumnSearchProps('intruck_kpi_move_per_h'),
            children:[
                {  
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_per_h',
                    key: 'intruck_kpi_move_per_h',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_per_h;
                        const bValue = b?.intruck_kpi_move_per_h;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
        },
        {
       
            title: '装船任务数',
            ...getColumnSearchProps('intruck_kpi_move_yv'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_yv',
                    width: 60,
                    key: 'intruck_kpi_move_yv',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_yv;
                        const bValue = b?.intruck_kpi_move_yv;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ],
        },
        {
          
            title: '卸船任务数',
            ...getColumnSearchProps('intruck_kpi_move_vy'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_vy',
                    width: 55,
                    key: 'intruck_kpi_move_vy',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_vy;
                        const bValue = b?.intruck_kpi_move_vy;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
           
        },
        {
            title: '堆场转堆任务数',
            ...getColumnSearchProps('intruck_kpi_move_yy'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_yy',
                    width: 65,
                    key: 'intruck_kpi_move_yy',
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_yy;
                        const bValue = b?.intruck_kpi_move_yy;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
          
        },
        {
         
            title: '重进重出任务数',
            ...getColumnSearchProps('intruck_kpi_move_heavy'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_heavy',
                    key: 'intruck_kpi_move_heavy',
                    width: 65,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_heavy;
                        const bValue = b?.intruck_kpi_move_heavy;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
        },
        {
            title: '重进重出比例',
            ...getColumnSearchProps('intruck_kpi_move_heavy_r'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_move_heavy_r',
                    key: 'intruck_kpi_move_heavy_r',
                    width: 60,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_move_heavy_r;
                        const bValue = b?.intruck_kpi_move_heavy_r;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
           
        },
        {
        
            title: '双箱任务数',
            ...getColumnSearchProps('intruck_kpi_two_cntr'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_two_cntr',
                    key: 'intruck_kpi_two_cntr',
                    width: 55,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_two_cntr;
                        const bValue = b?.intruck_kpi_two_cntr;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
           
        },
        {
       
            title: '双箱率',
            ...getColumnSearchProps('intruck_kpi_two_cntr_r'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_two_cntr_r',
                    key: 'intruck_kpi_two_cntr_r',
                    width: 45,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_two_cntr_r;
                        const bValue = b?.intruck_kpi_two_cntr_r;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
        },
        {
            title: '行驶距离',
            ...getColumnSearchProps('intruck_kpi_dis'),
            children:[
                {
                    title:'(1)',
                    dataIndex: 'intruck_kpi_dis',
                    key: 'intruck_kpi_dis',
                    width: 50,
                    sorter: (a, b) => {
                        const aValue = a?.intruck_kpi_dis;
                        const bValue = b?.intruck_kpi_dis;
                        return aValue - bValue
                    },
                    sortDirections: ['descend', 'ascend'],
                },
            ]
          
        },
    ];

    const fetchInit = async()=>{
        const truckList = truckSummaryData?.detail_info?.map((item:any)=>({text:item?.intruck_id,value:item?.intruck_id}))
        setTruckOptions(truckList) //集卡下拉框数据
        setCurrentTimeData(truckSummaryData?.detail_info)
        setCloneCurrentTimeData(truckSummaryData?.detail_info)
        setCurrentSummary(truckSummaryData?.average) // 当前时间数据， 表格下方平均数据
    }

    const fetchQueryTruckTimeOptions = async()=>{
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss')
        const result = await queryTruckTimeOptions({ResponseData:{}});
        if(result && result.resultCode == '200'){
            //
            const options = result?.resultData?.summary_id?.map((item:any)=>({label:`${moment(item?.save_time * 1000).format('YYYY-MM-DD HH:mm:ss')}(${item?.start_time}~${item?.end_time})`,value:item?.save_time}))
            selectForm?.setFieldsValue({firstTime:currentTime})
            setTimeOptions(options) 
        }else{
            message.error('查询下拉框时间失败！')
        }
    }
    // 根据选择的时间id 查询详细数据
    const fetchQueryDetailTruckSummaryByTime = async(summaryId:any)=>{
        const result = await queryTruckSummary({
            ResponseData:{
                summary_id:summaryId
            }
        })
        if(result && result.resultCode == '200'){
            setSelectHistoryData(true)
            if(!disableedFlag){
                setCurrentTimeData(result?.resultData?.TRUCK_SUMMARY?.detail_info)
                setCurrentSummary(result?.resultData?.TRUCK_SUMMARY?.average)
            }else{
                const mergedData = currentTimeData?.map((item1:any) => {
                    const item2 = result?.resultData?.TRUCK_SUMMARY?.detail_info?.find((item:any) => item.intruck_id === item1.intruck_id);
                    return { ...item1, ...item2 };
                });
                setCurrentTimeData(mergedData)
            }
            
        }else{
            message.error('查询失败！')
        }
    }
    const fetchSaveTruckInfo = async(detailInfo:any,average:any)=>{
       const params = {
        detail_info:detailInfo,
        average:average,
        rangeTime:rangeTime
       }
       const result = await saveTruckSummary({
        ResponseData:params
       })
       if(result&&result.resultCode == '200'){
        message.info('保存成功！')
        fetchQueryTruckTimeOptions()
       }else{
        message.error('保存失败！')
       }
    }
 
    useEffect(()=>{
        fetchQueryTruckTimeOptions()
    },[])
    useEffect(()=>{
        fetchInit()
    },[force])
  return (
    <>
       <Card style={{ marginBottom: '16px' }} bordered={false} title='集卡作业效率指标'>
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
            <Form form={selectForm} layout="inline">
            <Col >
            <Form.Item name='firstTime'>
              <Select
                allowClear
                onChange={(values:any)=>{
                    fetchQueryDetailTruckSummaryByTime(values)
                }}
                options={timeOptions}
                style={{width:450}}
              >
              </Select>
             
            </Form.Item>
          </Col>
          <Col >
          <Form.Item name='secondTime'>
              <Select
                allowClear
                onChange={(values:any)=>{
                    console.log('选择了',values);
                }}
                options={timeOptions}
                style={{width:450}}
              >
              </Select>
            </Form.Item>
          </Col>
            </Form>
          <Col md={8} sm={24}>
            <span>
              <Button
                style={{ marginLeft: '10px', marginRight: '40px', backgroundColor: 'rgba(115, 130, 204, 1)', color: '#fff', borderRadius: '4px', border: 'none' }}
                type="primary"
                onClick={async () => {
                    const formValues = selectForm.getFieldsValue();
                    if(!formValues?.firstTime || !formValues?.secondTime){
                        message.error('请选择数据后进行对比！');
                        return
                    }
                    console.log('对比',formValues?.secondTime);
                    const result = await queryTruckSummary({
                        ResponseData:{
                            summary_id:formValues?.secondTime
                        }
                    })
                    if(result&&result.resultCode == '200'){
                        const modifiedData2 = result?.resultData?.TRUCK_SUMMARY?.detail_info?.map((item:any) => {
                            const newItem:any = { intruck_id: item.intruck_id };
                            for (const [key, value] of Object.entries(item)) {
                              if (key !== 'intruck_id') {
                                newItem[`${key}_2`] = value;
                              }
                            }
                            return newItem;
                          });
                          const mergedData = currentTimeData?.map((item1:any) => {
                            const item2 = modifiedData2?.find((item:any) => item.intruck_id === item1.intruck_id);
                            return { ...item1, ...item2 };
                        });
                            // 平均
                        const combinedObj:any = {}
                        for (const [key, value] of Object.entries(result?.resultData?.TRUCK_SUMMARY?.average)) {
                            if (key !== 'intruck_id') {
                                combinedObj[`${key}_2`] = value;
                            }
                        }
                        setCurrentTimeData(mergedData)
                        setDuibiAllData(mergedData)
                        setCurrentSummary({...currentSummary,...combinedObj})
                        setDisabledFlag(true)
                    }
                }}
              >
                对比
              </Button>
              <Button
                style={{ marginLeft: '10px', marginRight: '40px', backgroundColor: 'rgba(103, 183, 155, 1)', color: '#fff', borderRadius: '4px', border: 'none' }}
                onClick={() => {
                    fetchQueryTruckTimeOptions()
                    fetchInit()
                    setDisabledFlag(false)
                    setSelectHistoryData(false)
                    selectForm?.setFieldsValue({secondTime:undefined})
                }}
              >
                重置
              </Button>
              <Button
                style={{ marginLeft: '10px', marginRight: '40px', backgroundColor: 'rgb(208, 194, 228)', color: '#fff', borderRadius: '4px', border: 'none' }}
                onClick={() => {
                    fetchSaveTruckInfo(currentTimeData,currentSummary)
                }}
                disabled={!!disableedFlag || !!selectHistoryData}
              >
                保存
              </Button>
              <Button
                style={{ marginLeft: '10px', marginRight: '40px', backgroundColor: 'rgba(115, 130, 204, 1)', color: '#fff', borderRadius: '4px', border: 'none' }}
                onClick={() => {
                    const exportData:any = [];
                    currentTimeData?.forEach((item:any)=>{
                        if(disableedFlag){
                            exportData.push({
                                '集卡编号':item?.intruck_id,
                                '周转时间(min)(1)':item?.intruck_kpi_cycle,
                                '周转时间(min)(2)':item?.intruck_kpi_cycle_2 ||null,
                                '总任务数(1)':item?.intruck_kpi_move,
                                '总任务数(2)':item?.intruck_kpi_move_2,
                                '每小时任务数(1)':item?.intruck_kpi_move_per_h,
                                '每小时任务数(2)':item?.intruck_kpi_move_per_h_2,
                                '装船任务数(1)':item?.intruck_kpi_move_yv,
                                '装船任务数(2)':item?.intruck_kpi_move_yv_2,
                                '卸船任务数(1)':item?.intruck_kpi_move_vy,
                                '卸船任务数(2)':item?.intruck_kpi_move_vy_2,
                                '堆场转堆任务数(1)':item?.intruck_kpi_move_yy,
                                '堆场转堆任务数(2)':item?.intruck_kpi_move_yy_2,
                                '重进重出任务数(1)':item?.intruck_kpi_move_heavy,
                                '重进重出任务数(2)':item?.intruck_kpi_move_heavy_2,
                                '重进重出比例(1)':item?.intruck_kpi_move_heavy_r,
                                '重进重出比例(2)':item?.intruck_kpi_move_heavy_r_2,
                                '双箱任务数(1)':item?.intruck_kpi_two_cntr,
                                '双箱任务数(2)':item?.intruck_kpi_two_cntr_2,
                                '双箱率(1)':item?.intruck_kpi_two_cntr_r,
                                '双箱率(2)':item?.intruck_kpi_two_cntr_r_2,
                                '行驶距离(1)':item?.intruck_kpi_dis,
                                '行驶距离(2)':item?.intruck_kpi_dis
                             })
                            
                        }else{
                            exportData.push({
                                '集卡编号':item?.intruck_id,
                                '周转时间(min)':item?.intruck_kpi_cycle,
                                '总任务数':item?.intruck_kpi_move,
                                '每小时任务数':item?.intruck_kpi_move_per_h,
                                '装船任务数':item?.intruck_kpi_move_yv,
                                '卸船任务数':item?.intruck_kpi_move_vy,
                                '堆场转堆任务数':item?.intruck_kpi_move_yy,
                                '重进重出任务数':item?.intruck_kpi_move_heavy,
                                '重进重出比例':item?.intruck_kpi_move_heavy_r,
                                '双箱任务数':item?.intruck_kpi_two_cntr,
                                '双箱率':item?.intruck_kpi_two_cntr_r,
                                '行驶距离':item?.intruck_kpi_dis,
                             })
                        }
                    })
                   if(disableedFlag){
                    const footer = {
                        '集卡编号':'合计',
                        '周转时间(min)(1)':currentSummary?.intruck_kpi_cycle || null,
                        '周转时间(min)(2)':currentSummary?.intruck_kpi_cycle_2 ||null,
                        '总任务数(1)':currentSummary?.intruck_kpi_move || null,
                        '总任务数(2)':currentSummary?.intruck_kpi_move_2 || null,
                        '每小时任务数(1)':currentSummary?.intruck_kpi_move_per_h || null,
                        '每小时任务数(2)':currentSummary?.intruck_kpi_move_per_h_2 || null,
                        '装船任务数(1)':currentSummary?.intruck_kpi_move_yv || null,
                        '装船任务数(2)':currentSummary?.intruck_kpi_move_yv_2 || null,
                        '卸船任务数(1)':currentSummary?.intruck_kpi_move_vy || null,
                        '卸船任务数(2)':currentSummary?.intruck_kpi_move_vy_2 || null,
                        '堆场转堆任务数(1)':currentSummary?.intruck_kpi_move_yy || null,
                        '堆场转堆任务数(2)':currentSummary?.intruck_kpi_move_yy_2 || null,
                        '重进重出任务数(1)':currentSummary?.intruck_kpi_move_heavy || null,
                        '重进重出任务数(2)':currentSummary?.intruck_kpi_move_heavy_2 || null,
                        '重进重出比例(1)':currentSummary?.intruck_kpi_move_heavy_r || null,
                        '重进重出比例(2)':currentSummary?.intruck_kpi_move_heavy_r_2 || null,
                        '双箱任务数(1)':currentSummary?.intruck_kpi_two_cntr || null,
                        '双箱任务数(2)':currentSummary?.intruck_kpi_two_cntr_2 || null,
                        '双箱率(1)':currentSummary?.intruck_kpi_two_cntr_r || null,
                        '双箱率(2)':currentSummary?.intruck_kpi_two_cntr_r_2 || null,
                        '行驶距离(1)':currentSummary?.intruck_kpi_dis || null,
                        '行驶距离(2)':currentSummary?.intruck_kpi_dis || null
                     }
                     exportData.push(footer)
                   }else{
                    const footer = {
                        '集卡编号':'合计',
                        '周转时间(min)':currentSummary?.intruck_kpi_cycle || null,
                        '总任务数':currentSummary?.intruck_kpi_move || null,
                        '每小时任务数':currentSummary?.intruck_kpi_move_per_h || null,
                        '装船任务数':currentSummary?.intruck_kpi_move_yv || null,
                        '卸船任务数':currentSummary?.intruck_kpi_move_vy || null,
                        '堆场转堆任务数':currentSummary?.intruck_kpi_move_yy || null,
                        '重进重出任务数':currentSummary?.intruck_kpi_move_heavy || null,
                        '重进重出比例':currentSummary?.intruck_kpi_move_heavy_r || null,
                        '双箱任务数':currentSummary?.intruck_kpi_two_cntr || null,
                        '双箱率':currentSummary?.intruck_kpi_two_cntr_r || null,
                        '行驶距离':currentSummary?.intruck_kpi_dis || null
                   }
                  exportData.push(footer)
                   }
                    exportToExcel(exportData,'集卡作业效率指标')
                }}
              >
                导出
              </Button>
            </span>
          </Col>
        </Row>
        <Table 
        rowKey={'intruck_id'} 
        columns={disableedFlag?columns_2:columns} 
        dataSource={currentTimeData} 
        pagination={false}
        scroll={{ y: minHeight }}
        summary={(pageData) => {
            let totalBorrow = 0;
            let totalRepayment = 0;
            pageData.forEach(({ borrow, repayment }) => {
              totalBorrow += borrow;
              totalRepayment += repayment;
            });
            return (
               <Table.Summary fixed>
                {
                    disableedFlag?<Table.Summary.Row>
                    <Table.Summary.Cell index={0}>平均</Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      { Number(currentSummary?.intruck_kpi_cycle).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                    { Number(currentSummary?.intruck_kpi_cycle_2).toFixed(2)|| ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>
                    { Number(currentSummary?.intruck_kpi_move).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4}>
                    { Number(currentSummary?.intruck_kpi_move_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>
                    { Number(currentSummary?.intruck_kpi_move_per_h).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6}>
                    { Number(currentSummary?.intruck_kpi_move_per_h_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7}>
                    { Number(currentSummary?.intruck_kpi_move_yv).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={8}>
                    { Number(currentSummary?.intruck_kpi_move_yv_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={9}>
                    { Number(currentSummary?.intruck_kpi_move_vy).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={10}>
                    {Number(currentSummary?.intruck_kpi_move_vy_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={11}>
                    { Number(currentSummary?.intruck_kpi_move_yy).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={12}>
                    { Number(currentSummary?.intruck_kpi_move_yy_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={13}>
                    { Number(currentSummary?.intruck_kpi_move_heavy).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={14}>
                    { Number(currentSummary?.intruck_kpi_move_heavy_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={15}>
                    { Number(currentSummary?.intruck_kpi_move_heavy_r).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={16}>
                    { Number(currentSummary?.intruck_kpi_move_heavy_r_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={17}>
                    { Number(currentSummary?.intruck_kpi_two_cntr).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={18}>
                    { Number(currentSummary?.intruck_kpi_two_cntr_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={19}>
                    { Number(currentSummary?.intruck_kpi_two_cntr_r).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={20}>
                    { Number(currentSummary?.intruck_kpi_two_cntr_r_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={21}>
                    { Number(currentSummary?.intruck_kpi_dis).toFixed(2) || ''}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={22}>
                    { Number(currentSummary?.intruck_kpi_dis_2).toFixed(2) || ''}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>:<Table.Summary.Row>
                  <Table.Summary.Cell index={0}>平均</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    { Number(currentSummary?.intruck_kpi_cycle).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                  { Number(currentSummary?.intruck_kpi_move).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}>
                  { Number(currentSummary?.intruck_kpi_move_per_h).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={4}>
                  { Number(currentSummary?.intruck_kpi_move_yv).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={5}>
                  { Number(currentSummary?.intruck_kpi_move_vy).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={6}>
                  { Number(currentSummary?.intruck_kpi_move_yy).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={7}>
                  { Number(currentSummary?.intruck_kpi_move_heavy).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={8}>
                  { Number(currentSummary?.intruck_kpi_move_heavy_r).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={9}>
                  { Number(currentSummary?.intruck_kpi_two_cntr).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={10}>
                  { Number(currentSummary?.intruck_kpi_two_cntr_r).toFixed(2) || ''}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={11}>
                  { Number(currentSummary?.intruck_kpi_dis).toFixed(2) || ''}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
                }
                
               </Table.Summary>
            );
          }}
        />

      </Card>

    </>
  )
}

export default TrackJobAnalysis;