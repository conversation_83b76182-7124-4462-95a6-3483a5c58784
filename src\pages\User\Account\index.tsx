import PermissionView from '@/components/PermissionView';
import {
  activeUserList,
  addUser,
  disableUserList,
  exportUserList,
  findUserByName,
  findUserGrantedMenuUiTree,
  resetPassword,
  updateUser,
  findUserPage
} from '@/services/base/user';
import {
  findRoleList
} from '@/services/base/role';
import { CheckCircleOutlined, CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { FooterToolbar, PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Popconfirm, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import CreateUserForm from './components/CreateUserForm';
import UpdateUserForm from './components/UpdateUserForm';

const Account: React.FC = () => {
  const [createUserFormVisible, setCreateUserFormVisible] = useState<boolean>(false);
  const [updateUserFormVisible, setUpdateUserFormVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentUser, setCurrentUser] = useState<API.UserCO>();
  const [selectedUsers, setSelectedUsers] = useState<API.UserQryCO[]>([]);
  const [queryRequest, setQueryRequest] = useState<API.SimpleQueryRequest>({});
  const [permissionViewOpen, setPermissionViewOpen] = useState<boolean>(false);
  const [grantedMenuUITreeData, setGrantedMenuUITreeData] = useState<API.MenuUICO[]>([]);

  const [roleMap, setRoleMap] = useState(new Map());
  const intl = useIntl();

  const userLoginIdFormat = intl.formatMessage({
    id: 'userLoginId',
  });
  const userUserNameFormat = intl.formatMessage({
    id: 'userUserName',
  });
  const userProfilePhotoFormat = intl.formatMessage({
    id: 'userProfilePhoto',
  });
  const userTelephoneNumberFormat = intl.formatMessage({
    id: 'userTelephoneNumber',
  });
  const basicEmailFormat = intl.formatMessage({
    id: 'basicEmail',
  });
  const userStatusFormat = intl.formatMessage({
    id: 'userStatus',
  });
  const userLastLoginTimeFormat = intl.formatMessage({
    id: 'userLastLoginTime',
  });
  const userGroupDisableFormat = intl.formatMessage({
    id: 'userGroupDisable',
  });
  const userGroupEnableFormat = intl.formatMessage({
    id: 'userGroupEnable',
  });
  const userModificationTimeFormat = intl.formatMessage({
    id: 'userModificationTime',
  });
  const userOptionsFormat = intl.formatMessage({
    id: 'userOptions',
  });
  const userEditFormat = intl.formatMessage({
    id: 'userEdit',
  });
  const userDisableUserGroupFormat = intl.formatMessage({
    id: 'userDisableUserGroup',
  });
  const userDisableUserGroupSuccessfulFormat = intl.formatMessage({
    id: 'userDisableUserGroupSuccessful',
  });
  const userDisableUserGroupFailureFormat = intl.formatMessage({
    id: 'userDisableUserGroupFailure',
  });
  const userGroupDisableUserGroupConfirmFormat = intl.formatMessage({
    id: 'userGroupDisableUserGroupConfirm',
  });
  const userGroupDisableUserGroupCancelFormat = intl.formatMessage({
    id: 'userGroupDisableUserGroupCancel',
  });
  const userDisableFormat = intl.formatMessage({
    id: 'userDisable',
  });
  const userResetPasswordFormat = intl.formatMessage({
    id: 'userResetPassword',
  });
  const userPreviewAuthorityFormat = intl.formatMessage({
    id: 'userPreviewAuthority',
  });
  const userManagementFormat = intl.formatMessage({
    id: 'userManagement',
  });
  const userActiveSuccessfulFormat = intl.formatMessage({
    id: 'userActiveSuccessful',
  });
  const userRecreateConfirmationFormat = intl.formatMessage({
    id: 'userRecreateConfirmation',
  });
  const userActiveFailureFormat = intl.formatMessage({
    id: 'userActiveFailure',
  });
  const userPasswordRecreateConfirmationFormat = intl.formatMessage({
    id: 'userPasswordRecreateConfirmation',
  });
  const userSelectedFormat = intl.formatMessage({
    id: 'userSelected',
  });
  const userItemFormat = intl.formatMessage({
    id: 'userItem',
  });
  const userAdditionFormat = intl.formatMessage({
    id: 'userAddition',
  });
  const userExportFormat = intl.formatMessage({
    id: 'userExport',
  });
  const userAddingUserFormat = intl.formatMessage({
    id: 'userAddingUser',
  });
  const userAdditionSuccessfulFormat = intl.formatMessage({
    id: 'userAdditionSuccessful',
  });
  const userAdditionFailureFormat = intl.formatMessage({
    id: 'userAdditionFailure',
  });
  const userModifyingUserFormat = intl.formatMessage({
    id: 'userModifyingUser',
  });
  const userUserModificationSuccessful = intl.formatMessage({
    id: 'userUserModificationSuccessful',
  });
  const userUserModificationFailure = intl.formatMessage({
    id: 'userUserModificationFailure',
  });
  const userResetingPassword = intl.formatMessage({
    id: 'userResetingPassword',
  });
  const userPasswordSuccessfullyResetInitialPassword = intl.formatMessage({
    id: 'userPasswordSuccessfullyResetInitialPassword',
  });
  const userResetingPasswordFailure = intl.formatMessage({
    id: 'userResetingPasswordFailure',
  });

  useEffect(() => {
    findRoleList({}).then((res) => {
      const roleMap = new Map();
      res?.data?.forEach((role) => {
        roleMap.set(role.id, role.name);
      });
      setRoleMap(roleMap);
    });
  }, []);

  const enabledMap = new Map();
  enabledMap.set(true, userGroupEnableFormat);
  enabledMap.set(false, userGroupDisableFormat);

  const addUserCmd = async (fields: API.UserAddCO) => {
    const hide = message.loading(userAddingUserFormat);
    const temp: API.UserAddCO = { ...fields };
    try {
      const result = await addUser({ userAddCO: { ...temp } });
      hide();
      if (result.success) {
        message.success(userAdditionSuccessfulFormat);
        return true;
      }

      message.error(result.message ?? '');
      return false;
    } catch (error) {
      hide();
      message.error(userAdditionFailureFormat);
      return false;
    }
  };

  const updateUserCmd = async (fields: API.UserUpdateCO) => {
    const hide = message.loading(userModifyingUserFormat);
    const temp: API.UserUpdateCO = { ...fields };
    try {
      const result = await updateUser({ userUpdateCO: { ...currentUser, ...temp } });
      hide();
      if (result && result.success) {
        message.success(userUserModificationSuccessful);
        return true;
      }
      message.error(userUserModificationFailure);
      return false;
    } catch (error) {
      hide();
      message.error(userUserModificationFailure);
      return false;
    }
  };

  const resetPasswordCmd = async (userId: number) => {
    const hide = message.loading(userResetingPassword);
    try {
      const res = await resetPassword({ userId: userId });
      hide();
      if (res.success) {
        message.success(userPasswordSuccessfullyResetInitialPassword);
        return true;
      }
      message.error(res.message ?? '');
      return false;
    } catch (error) {
      hide();
      message.error(userResetingPasswordFailure);
      return false;
    }
  };

  const columns: ProColumns<API.UserQryCO>[] = [
    {
      title: userLoginIdFormat,
      dataIndex: 'name',
      sorter: true,
    },
    {
      title: userUserNameFormat,
      dataIndex: 'fullName',
      sorter: true,
    },
    {
      title: userProfilePhotoFormat,
      dataIndex: 'avatarUrl',
      valueType: 'avatar',
      search: false,
    },
    {
      title: userTelephoneNumberFormat,
      dataIndex: 'phoneNumber',
      sorter: true,
    },
    {
      title: basicEmailFormat,
      dataIndex: 'email',
      sorter: true,
    },
    {
      title: '所属角色',
      dataIndex: 'roleIds',
      valueType: 'select',
      valueEnum: roleMap,
      render: (text: any, record: API.UserQryCO) => {
        if (record.roleIds && record.roleIds.length > 0) {
          return (
            <>
              {record.roleIds.map((roleId) => (
                roleMap.has(roleId) ? (
                  <Tag key={roleId} color="blue" style={{ marginRight: 4, marginBottom: 4 }}>
                    {roleMap.get(roleId)}
                  </Tag>
                ) : null
              ))}
            </>
          );
        }
        return null;
      },
    },
    {
      title: userStatusFormat,
      dataIndex: 'enabled',
      initialValue: true,
      valueType: 'select',
      valueEnum: enabledMap,
      render: (text: any, record: API.UserQryCO) => {
        return (
          <>
            {record.enabled ? (
              <Tag icon={<CheckCircleOutlined />} color="success">
                {userGroupEnableFormat}
              </Tag>
            ) : (
              <Tag icon={<CloseCircleOutlined />} color="error">
                {userGroupDisableFormat}
              </Tag>
            )}
          </>
        );
      },
    },
    {
      title: userLastLoginTimeFormat,
      dataIndex: 'lastLoginTime',
      valueType: 'dateTime',
      search: false,
      sorter: true,
    },
    {
      title: userModificationTimeFormat,
      dataIndex: 'updateTime',
      valueType: 'date',
      search: false,
      sorter: true,
    },
    {
      title: userOptionsFormat,
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={async () => {
            const res = await findUserByName({ username: record?.name });
            if (res?.success) {
              const data = res?.data;
              setUpdateUserFormVisible(true);
              const currentRecord = {
                ...record,
                identityIds: data?.identityIds ?? [],
                groupIds: data?.groupIds ?? [],
              };
              setCurrentUser(currentRecord);
            }
          }}
        >
          {userEditFormat}
        </a>,

        record.enabled ? (
          <Popconfirm
            key="DisableUser"
            title={userDisableUserGroupFormat}
            onConfirm={async () => {
              const res = await disableUserList({ userIds: [record.id ?? '0'] });
              if (res?.success) {
                actionRef.current?.reloadAndRest?.();
                message.success(userDisableUserGroupSuccessfulFormat);
              } else {
                message.error(userDisableUserGroupFailureFormat);
              }
            }}
            okText={userGroupDisableUserGroupConfirmFormat}
            cancelText={userGroupDisableUserGroupCancelFormat}
          >
            <a key="disable">{userDisableFormat}</a>
          </Popconfirm>
        ) : (
          <Popconfirm
            key="activeUser"
            title={userRecreateConfirmationFormat}
            onConfirm={async () => {
              const res = await activeUserList({ userIds: [record.id ?? '0'] });
              if (res?.success) {
                actionRef.current?.reloadAndRest?.();
                message.success(userActiveSuccessfulFormat);
              } else {
                message.error(userActiveFailureFormat);
              }
            }}
            okText={userGroupDisableUserGroupConfirmFormat}
            cancelText={userGroupDisableUserGroupCancelFormat}
          >
            <a key="enable">{userGroupEnableFormat}</a>
          </Popconfirm>
        ),
        <Popconfirm
          key="resetPassword"
          title={userPasswordRecreateConfirmationFormat}
          onConfirm={async () => {
            const rest = await resetPasswordCmd(record.id ?? 0);
            if (rest) {
              actionRef.current?.reloadAndRest?.();
            }
          }}
          okText={userGroupDisableUserGroupConfirmFormat}
          cancelText={userGroupDisableUserGroupCancelFormat}
        >
          <a key="reset">{userResetPasswordFormat}</a>
        </Popconfirm>,
        <a
          key="viewDepartmentPermissions"
          onClick={async () => {
            const result = await findUserGrantedMenuUiTree({
              userId: record?.id ?? '0',
            });
            setGrantedMenuUITreeData(result.data ?? []);
            setPermissionViewOpen(true);
          }}
        >
          {userPreviewAuthorityFormat}
        </a>,
      ],
    },
  ];

  return (
    <>
      <PageContainer
        header={{
          title: null,
          breadcrumb: {},
        }}
      >
        <ProTable<API.UserQryCO>
          headerTitle={userManagementFormat}
          actionRef={actionRef}
          rowKey="id"
          search={{ labelWidth: 120 }}
          toolBarRender={() => [
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setCreateUserFormVisible(true);
              }}
            >
              <PlusOutlined />
              <FormattedMessage id="new" defaultMessage={userAdditionFormat} />
            </Button>,
            <Button
              type="default"
              key="export"
              onClick={async () => {
                if (!queryRequest.pagination) {
                  return;
                }
                await exportUserList(queryRequest, { responseType: 'blob' }).then((data) => {
                  const blobUrl = window.URL.createObjectURL(data);
                  const a = document.createElement('a');
                  a.style.display = 'none';
                  a.download = 'users.csv';
                  a.href = blobUrl;
                  a.click();
                });
              }}
            >
              <PlusOutlined />
              <FormattedMessage id="export" defaultMessage={userExportFormat} />
            </Button>,
          ]}
          columns={columns}
          request={(params, sorter) => {
            const { current, pageSize, ...query } = params;
            const body = {
              filter: { ...query },
              pagination: { current: (current ?? 1) - 1, pageSize },
              sorter: { ...sorter },
            };
            setQueryRequest(body);
            return findUserPage(body);
          }}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedUsers(selectedRows);
            },
          }}
          pagination={{ pageSize: 10 }}
        />
      </PageContainer>
      {selectedUsers?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              {userSelectedFormat}
              <a style={{ fontWeight: 600 }}>{selectedUsers.length}</a> {userItemFormat}
            </div>
          }
        ></FooterToolbar>
      )}
      {createUserFormVisible && (
        <CreateUserForm
          onSubmit={async (value: API.UserAddCO) => {
            const success = await addUserCmd(value);
            if (success) {
              setCreateUserFormVisible(false);
              setCurrentUser(undefined);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }
          }}
          onVisibleChange={(visible: boolean) => {
            setCreateUserFormVisible(visible);
          }}
          createUserFormVisible={createUserFormVisible}
          values={{}}
        />
      )}
      {updateUserFormVisible && (
        <UpdateUserForm
          visible={updateUserFormVisible}
          visibleHandler={(visible: boolean) => {
            setUpdateUserFormVisible(visible);
          }}
          initValue={{ ...currentUser }}
          onSubmit={async (value: API.UserUpdateCO) => {
            const success = await updateUserCmd(value);
            if (success) {
              setUpdateUserFormVisible(false);
              setCurrentUser(undefined);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }
            return success;
          }}
        />
      )}
      <PermissionView
        modalOpen={permissionViewOpen}
        handleModalOpen={setPermissionViewOpen}
        grantedMenuUITreeData={grantedMenuUITreeData}
      />
    </>
  );
  return <></>;
};

export default Account;
