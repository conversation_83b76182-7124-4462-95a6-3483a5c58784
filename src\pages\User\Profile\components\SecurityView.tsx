/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @version: 2.0
 * @Date: 2021-09-06 13:26:27
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-06-18 08:55:53
 * @Descripttion:
 */
import { changePassword } from '@/services/base/user';
import { List, message } from 'antd';
import { useState } from 'react';
import { useIntl } from 'umi';
import ChangePasswordModal from './ChangePasswordModal';

type Unpacked<T> = T extends (infer U)[] ? U : T;

const SecurityView: React.FC = () => {
  const [changePasswordFormVisible, setChangePasswordFormVisible] = useState<boolean>(false);
  const intl = useIntl();
  const StrongFormat = intl.formatMessage({
    id: 'Strong',
  });
  const MediumFormat = intl.formatMessage({
    id: 'Medium',
  });
  const LowFormat = intl.formatMessage({
    id: 'Low',
  });
  const securityAccountPasswordFormat = intl.formatMessage({
    id: 'securityAccountPassword',
  });
  const securityCurrentPasswordStrengthFormat = intl.formatMessage({
    id: 'securityCurrentPasswordStrength',
  });
  const securityEditFormat = intl.formatMessage({
    id: 'securityEdit',
  });
  const securityEditSuccessFormat = intl.formatMessage({
    id: 'securityEditSuccess',
  });
  const passwordStrength = {
    strong: <span className="strong">{StrongFormat}</span>,
    medium: <span className="medium">{MediumFormat}</span>,
    weak: <span className="weak">{LowFormat} Weak</span>,
  };
  const getData = () => [
    {
      title: securityAccountPasswordFormat,
      description: (
        <>
          {securityCurrentPasswordStrengthFormat}：{passwordStrength.strong}
        </>
      ),
      actions: [
        <a
          key="Modify"
          onClick={() => {
            setChangePasswordFormVisible(true);
          }}
        >
          {securityEditFormat}
        </a>,
      ],
    },
  ];

  const data = getData();
  return (
    <>
      <List<Unpacked<typeof data>>
        itemLayout="horizontal"
        dataSource={data}
        renderItem={(item) => (
          <List.Item actions={item.actions}>
            <List.Item.Meta title={item.title} description={item.description} />
          </List.Item>
        )}
      />
      {
        <ChangePasswordModal
          onSubmit={async (value: API.ChangeUserPasswordCO) => {
            const response = await changePassword({
              changeUserPasswordCO: value,
            });
            if (response.success) {
              setChangePasswordFormVisible(false);
              message.success(securityEditSuccessFormat);
            }
          }}
          onVisibleChange={(flag: boolean) => {
            setChangePasswordFormVisible(flag);
          }}
          changePasswordFormVisible={changePasswordFormVisible}
        />
      }
    </>
  );
};

export default SecurityView;
