/*
 * @Description:
 * @Version: 2.0
 * @Author: huzhenghui
 * @Date: 2022-08-19 16:46:17
 * @LastEditors: sr
 * @LastEditTime: 2025-03-20 13:36:09
 */
import { findRoleList } from '@/services/base/role';
import ProForm, { ModalForm, ProFormCheckbox, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Form, TreeSelect } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { useEffect, useState } from 'react';
import { useIntl } from 'umi';

type UpdateUserFormProps = {
  visible: boolean;
  visibleHandler: (visible: boolean) => void;
  onSubmit?: any;
  initValue: any;
};

const formatAllRoles = (roles: API.RoleCO[]): any[] => {
  const formattedRoles: any[] = roles.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
  return formattedRoles;
}

const UpdateUserForm: React.FC<UpdateUserFormProps> = ({
  visible,
  visibleHandler,
  onSubmit,
  initValue,
}) => {
  const [form] = Form.useForm();
  const [roleSelectItems, setRoleSelectItems] = useState<any[]>([]);
  const intl = useIntl();

  const userUserFormat = intl.formatMessage({
    id: 'userUser',
  });
  const userLoginIdFormat = intl.formatMessage({
    id: 'userLoginId',
  });
  const userEnterLoginIdLessThan50Characters = intl.formatMessage({
    id: 'userEnterLoginIdLessThan50Characters',
  });
  const userFullnameFormat = intl.formatMessage({
    id: 'userFullname',
  });
  const userEnterFullNameLessThan50Characters = intl.formatMessage({
    id: 'userEnterFullNameLessThan50Characters',
  });
  const userTelephoneNumber = intl.formatMessage({
    id: 'userTelephoneNumber',
  });
  const userTelephoneNumberFormatIncorrect = intl.formatMessage({
    id: 'userTelephoneNumberFormatIncorrect',
  });
  const basicEmailFormat = intl.formatMessage({
    id: 'basicEmail',
  });
  const userEmailFormatIncorrect = intl.formatMessage({
    id: 'userEmailFormatIncorrect',
  });
  const userActivatedorNot = intl.formatMessage({
    id: 'userActivatedorNot',
  });

  useEffect(() => {
    findRoleList({}).then((res) => {
      const formattedRoles = formatAllRoles(res.data ?? []);
      setRoleSelectItems(formattedRoles);
    });
  }, []);

  return (
    <ModalForm
      form={form}
      title={userUserFormat}
      visible={visible}
      onFinish={onSubmit}
      onVisibleChange={visibleHandler}
      initialValues={{
        ...initValue,
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <Form.Item noStyle name={'id'} />
      <ProForm.Group>
        <ProFormText
          name="name"
          label={userLoginIdFormat}
          width="md"
          allowClear={false}
          rules={[
            {
              required: true,
              message: userEnterLoginIdLessThan50Characters,
              max: 50,
            },
          ]}
        />
        <ProFormText
          name="fullName"
          label={userFullnameFormat}
          width="md"
          allowClear={false}
          rules={[
            {
              required: true,
              message: userEnterFullNameLessThan50Characters,
              max: 50,
            },
          ]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="phoneNumber"
          label={userTelephoneNumber}
          rules={[
            {
              pattern: /^\d{11}$/,
              message: userTelephoneNumberFormatIncorrect,
            },
          ]}
        />
        <ProFormText
          width="md"
          name="email"
          label={basicEmailFormat}
          rules={[
            {
              type: 'email',
              message: userEmailFormatIncorrect,
            },
          ]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          name="roleIds"
          label="角色"
          width="md"
          options={roleSelectItems}
          mode="multiple"
          rules={[{ required: true, message: '请选择角色!' }]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormCheckbox name="enabled" label={userActivatedorNot} width="md" allowClear={false} />
      </ProForm.Group>
    </ModalForm>
  );
};
export default UpdateUserForm;
