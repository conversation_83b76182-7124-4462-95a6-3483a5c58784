/*
 * @Descripttion: 
 * @Author: miluo-刘书记
 * @Date: 2024-07-23 14:12:03
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-07-23 16:36:35
 */
import { ModalForm, ProFormDigit, ProFormText } from '@ant-design/pro-components';
import {useIntl} from "umi";
import React from 'react';

export type UIPermissionFormProps = {
  onSubmit: (values?: any) => Promise<void>;
  modalOpen: boolean;
  handleModalOpen?: (modalOpen: boolean) => void;
  values?: Partial<API.PermissionUICO>;
};

const UIPermissionForm: React.FC<UIPermissionFormProps> = (props) => {
  const intl = useIntl();

  const menuEdit = intl.formatMessage({
    id: 'menuEdit',
  });
  const menuAddition = intl.formatMessage({
    id: 'menuAddition',
  });
  const pageAuthority = intl.formatMessage({
    id: 'pageAuthority',
  });
  const sequence = intl.formatMessage({
    id: 'sequence',
  });
  const pageCode = intl.formatMessage({
    id: 'pageCode',
  });
  const pageName = intl.formatMessage({
    id: 'pageName',
  });
  const userPleaseEnter = intl.formatMessage({
    id: 'userPleaseEnter',
  });

  return (
    <>
      <ModalForm
        title={`${!!props.values ? menuEdit : menuAddition}${pageAuthority}`}
        width="420px"
        style={{ padding: '32px 32px 16px' }}
        open={props.modalOpen}
        onOpenChange={props.handleModalOpen}
        initialValues={{ ...props.values }}
        onFinish={async (value) => {
          props.onSubmit(value);
        }}
        modalProps={{
          destroyOnClose: true,
        }}
      >
        <ProFormText
          label={pageCode}
          rules={[
            {
              required: true,
              message: userPleaseEnter,
              max: 100,
            },
          ]}
          width="md"
          name="code"
        />
        <ProFormText
          label={pageName}
          rules={[
            {
              required: true,
              message: userPleaseEnter,
              max: 200,
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormDigit label={sequence} name="sequence" width="md" min={1} max={1000} />
      </ModalForm>
    </>
  );
};

export default UIPermissionForm;
