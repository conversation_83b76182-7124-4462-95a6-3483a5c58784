import {
  addPermissionUi,
  delPermissionUi,
  findMenuTree,
  findPermissionUiPage,
  reOrderPermissionUi,
  updatePermissionUi,
} from '@/services/base/menu';
import { ExportOutlined, MenuOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  DragSortTable,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useMount } from 'ahooks';
import { Button, Layout, Menu, MenuProps, Popconfirm } from 'antd';
import {useIntl} from "umi";
import React, { useRef, useState } from 'react';
import UIPermissionForm from './components/UIPermissionForm';

const { Content, Sider } = Layout;

const MenuUI: React.FC = () => {
  const ACTIVE_MENU_ITEM_KEY = '2';
  const OPEN_SUB_MENU_ITEM_KEY = '1';
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [selectMenuId, setSelectMenuId] = useState<string>(ACTIVE_MENU_ITEM_KEY);
  const tableActionRef = useRef<ActionType>();
  const [uiPermissionModalOpen, setUIPermissionModalOpen] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<API.PermissionUICO | undefined>(undefined);
  const [flatMenuItems, setFlatMenuItems] = useState<Map<string, API.MenuCO>>();
  const [codeGenModalOpen, setCodeGenModalOpen] = useState<boolean>(false);
  const [uiCode, setUICode] = useState<string>('');
  const [permissionUIList, setPermissionUIList] = useState<API.PermissionUICO[]>([]);
  const intl = useIntl();

  const menuOrderFormat = intl.formatMessage({
    id: 'menuOrder',
  });
  const menuIDFormat = intl.formatMessage({
    id: 'menuID',
  });
  const menuNameFormat = intl.formatMessage({
    id: 'menuName',
  });
  const menuCodeFormat = intl.formatMessage({
    id: 'menuCode',
  });
  const menuCreatorFormat = intl.formatMessage({
    id: 'menuCreator',
  });
  const menuCreationTimeFormat = intl.formatMessage({
    id: 'menuCreationTime',
  });
  const menuModifierFormat = intl.formatMessage({
    id: 'menuModifier',
  });
  const menuModificationTimeFormat = intl.formatMessage({
    id: 'menuModificationTime',
  });
  const menuOptionsFormat = intl.formatMessage({
    id: 'menuOptions',
  });
  const menuEdit = intl.formatMessage({
    id: 'menuEdit',
  });
  const menuConfirmDeleteAuthority = intl.formatMessage({
    id: 'menuConfirmDeleteAuthority',
  });
  const menuDelate = intl.formatMessage({
    id: 'menuDelate',
  });
  const menuConfirm = intl.formatMessage({
    id: 'menuConfirm',
  });
  const menuCancel = intl.formatMessage({
    id: 'menuCancel',
  });
  const menuAddition = intl.formatMessage({
    id: 'menuAddition',
  });
  const menuExport = intl.formatMessage({
    id: 'menuExport',
  });

  type MenuItem = Required<MenuProps>['items'][number];

  const getItem = (
    label: React.ReactNode,
    key: React.Key,
    children?: MenuItem[],
    type?: 'group',
  ): MenuItem => {
    if (children?.length === 0) {
      return {
        key,
        label,
        type,
      } as MenuItem;
    }

    return {
      key,
      children,
      label,
      type,
    } as MenuItem;
  };

  const convertToMenu = (
    menuTree: API.MenuCO[],
    flatMenuItems: Map<string, API.MenuCO>,
  ): MenuItem[] => {
    return menuTree.map((item) => {
      flatMenuItems.set(item.id?.toString() ?? '', item);
      return getItem(
        item.name,
        item.id ?? 0,
        item.children ? convertToMenu(item.children, flatMenuItems) : [],
      );
    });
  };

  const handlePermissionUIDragSortEnd = (
    beforeIndex: number,
    afterIndex: number,
    newDataSource: API.PermissionUICO[],
  ) => {
    const ids = newDataSource.map((item) => item.id);
    reOrderPermissionUi({ ids });
    tableActionRef.current?.reload();
  };

  useMount(async () => {
    let menuTree = await findMenuTree();
    const flatMenuItems = new Map<string, API.MenuCO>();
    const menuItems = convertToMenu(menuTree.data ?? [], flatMenuItems);
    setFlatMenuItems(flatMenuItems);
    setMenuItems(menuItems);
  });

 const columns: ProColumns[] = [
    {
      title: menuOrderFormat,
      dataIndex: 'sequence',
      hideInSearch: true,
    },
    {
      title: menuIDFormat,
      dataIndex: 'id',
      hideInSearch: true,
    },
    {
      title: menuCodeFormat,
      dataIndex: 'code',
    },
    {
      title: menuNameFormat,
      dataIndex: 'name',
    },
    {
      title: '菜单标识',
      dataIndex: 'menuId',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '权限标识',
      dataIndex: 'permissionId',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: menuCreatorFormat,
      dataIndex: 'creator',
      hideInSearch: true,
    },
    {
      title: menuCreationTimeFormat,
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: menuModifierFormat,
      dataIndex: 'modifier',
      hideInSearch: true,
    },
    {
      title: menuModificationTimeFormat,
      dataIndex: 'updateTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: menuOptionsFormat,
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            setCurrentRecord(record);
            setUIPermissionModalOpen(true);
          }}
        >
          {menuEdit}
        </a>,
        <Popconfirm
          key="pcDelete"
          title={menuConfirmDeleteAuthority}
          onConfirm={async () => {
            const permissionUIDelCmd = {
              permissionUIId: record.id,
              permissionId: record.permissionId,
            };
            const rest = await delPermissionUi(permissionUIDelCmd);
            if (rest) {
              tableActionRef.current?.reloadAndRest?.();
            }
          }}
          okText={menuConfirm}
          cancelText={menuCancel}
        >
          <a key="delete">{menuDelate}</a>
        </Popconfirm>,
      ],
    },
  ];

  const dragHandleRender = () => (
    <>
      <MenuOutlined style={{ cursor: 'grab', color: 'gold' }} />
    </>
  );

  return (
    < 
    >
      <Layout>
        <Sider width={300}>
          <Menu
            mode="inline"
            style={{ height: '100%' }}
            items={menuItems}
            defaultOpenKeys={[OPEN_SUB_MENU_ITEM_KEY]}
            defaultSelectedKeys={[ACTIVE_MENU_ITEM_KEY]}
            onClick={({ key }) => {
              setSelectMenuId(key);
              tableActionRef.current?.reload();
            }}
          />
        </Sider>
        <Content style={{ padding: '0 24px', minHeight: 100 }}>
          <DragSortTable
            columns={columns}
            rowKey="id"
            pagination={false}
            actionRef={tableActionRef}
            dragSortKey="sequence"
            onDragSortEnd={handlePermissionUIDragSortEnd}
            dragSortHandlerRender={dragHandleRender}
            toolBarRender={() => [
              <Button
                type="primary"
                key="add"
                onClick={() => {
                  setCurrentRecord({ id: '', sequence: (permissionUIList?.length ?? 0) + 1 });
                  setUIPermissionModalOpen(true);
                }}
              >
                <PlusOutlined />
                {menuAddition}
              </Button>,
              <Button
                type="default"
                key="export"
                onClick={() => {
                  const menuItem = flatMenuItems?.get(selectMenuId);
                  const uiCode = `'${menuItem?.code}':{${permissionUIList
                    .map((item) => `${item.code?.toUpperCase()}:'${menuItem?.code}_${item.code}'`)
                    .join(',')}}`;
                  setUICode(uiCode);
                  setCodeGenModalOpen(true);
                }}
              >
                <ExportOutlined />
                {menuExport}
              </Button>,
            ]}
            request={async (params) => {
              const { ...query } = params;
              if (!!selectMenuId) {
                query.menuId = selectMenuId;
              }
              const body = {
                filter: { ...query },
                pagination: { current: 0, pageSize: 1000 },
                sorter: { sequence: 'asc' },
              };
              const permissionUiList = await findPermissionUiPage(body);
              setPermissionUIList(permissionUiList.data ?? []);
              return permissionUiList;
            }}
          />
        </Content>
      </Layout>

      <UIPermissionForm
        onSubmit={async (value) => {
          if (!!!currentRecord || currentRecord.id === '') {
            const permissionUIAddCmd = { permissionUICO: { ...value, menuId: selectMenuId } };
            const result = await addPermissionUi(permissionUIAddCmd);
            if (!!result.success) {
              setUIPermissionModalOpen(false);
              if (tableActionRef.current) {
                tableActionRef.current.reload();
              }
            }
          } else {
            const permissionUIUpdateCmd = { permissionUICO: { ...currentRecord, ...value } };
            const result = await updatePermissionUi(permissionUIUpdateCmd);
            if (!!result.success) {
              setUIPermissionModalOpen(false);
              if (tableActionRef.current) {
                tableActionRef.current.reload();
              }
            }
          }
        }}
        modalOpen={uiPermissionModalOpen}
        values={currentRecord}
        handleModalOpen={(uiPermissionModalOpen) => {
          setUIPermissionModalOpen(uiPermissionModalOpen);
        }}
      />

      <ModalForm
        title={'(config\\uiPermission.ts)'}
        width="600px"
        initialValues={{ code: uiCode }}
        open={codeGenModalOpen}
        modalProps={{
          destroyOnClose: true,
        }}
        submitter={{
          submitButtonProps: {
            style: {
              display: 'none',
            },
          },
        }}
        onOpenChange={setCodeGenModalOpen}
      >
        <ProFormTextArea colProps={{ span: 24 }} name="code" />
      </ModalForm>
    </>
  );
};

export default MenuUI;
