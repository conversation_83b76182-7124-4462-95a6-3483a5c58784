# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user  root;
worker_processes  1;

error_log  /var/log/nginx/error.log;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;
    proxy_request_buffering off;
    proxy_buffering off;
    #开启gzip
    gzip                on;
    gzip_buffers        32 4k;
    gzip_comp_level     6;
    gzip_min_length     1k;
    gzip_types          text/plain text/xml text/css application/javascript image/jpeg 		image/gif image/png font/ttf font/opentype font/x-woff image/svg+xml;

    include /etc/nginx/conf.d/*.conf;

    server {
         listen 443 ssl;
         server_name miluo-pc-ui;

         ssl_certificate /etc/nginx/ssl/hb.crt;
         ssl_certificate_key /etc/nginx/ssl/hb.key;

         ssl_protocols TLSv1.2 TLSv1.3;
         ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384';
         ssl_prefer_server_ciphers off;
         ssl_session_cache shared:SSL:10m;
         ssl_session_timeout 10m;

         index index.html index.htm index.php;
         add_header Cache-Control no-cache;


         location / {
           root   /usr/share/nginx/html;
           index  index.html index.htm;
           try_files $uri $uri/ /index.html;
         }
         location /tps_uiServer/ {
            proxy_pass http://**********:7088/;
            proxy_read_timeout 3600;
         }
         location /tps_truckQuery/ {
            proxy_pass http://**********:8890/;
            proxy_read_timeout 3600;
         }
         location /base/ {
            proxy_pass https://**********:8443/tps-permission-service/;
         }
    }


}
