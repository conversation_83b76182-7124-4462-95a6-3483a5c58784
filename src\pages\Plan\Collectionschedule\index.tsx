import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Form, Button, Tabs, message,List } from 'antd';
import {
  ProFormSelect,
  ProFormDateTimeRangePicker,
  ProFormDateRangePicker,
  ProFormDatePicker,
} from '@ant-design/pro-form';
import AgvEfficientOeeChart from './components/AvgEfficiencyOEE';
import TrackMovingGraph from './components/TrackMovingGraph';
import TrackJobAnalysis from './components/TrackJobAnalysis';
import TruckTransportVolumn from './components/TruckTransportVolumn';
import TruckQueueAnalysis from './components/TruckQueueAnalysis';
import TruckIndicator from './components/TruckIndicator';

import { queryTpsData, fetchGenerateTpsEchartsData } from '@/services/ascAutoDigitalwin/api';
import moment from 'moment';

const { TabPane } = Tabs;

const ScheduleAnalysis: React.FC = () => {
  const [form] = Form.useForm();
  const [traceForm] = Form.useForm();
  const [summaryForm] = Form.useForm();

  // OEE图
  const [chartOEEData, setChartOEEData] = useState<any>([]);
  const [chartOEEAvg,setChartOEEAvg] = useState<any>()
  const [oeeVisiable, setOeeVisiable] = useState<boolean>(false);
  const [oeeVesselNameOptions, setOeeVesselNameOptions] = useState<any>([]);
  const [oeeTruckNoOptions, setOeeTruckNoOptions] = useState<any>([]);
  const [oeeRouteOptions, setOeeRouteOptions] = useState<any>([]);

  // 集卡移动轨迹
  const [chartTruckMoveData, setChartTruckMoveData] = useState<any>([]);
  const [truckTraceVisiable, setTruckTraceVisiable] = useState<boolean>(false);
  const [truckVesselNameOptions, setTruckVesselNameOptions] = useState<any>([]);
  const [truckTruckNoOptions, setTruckTruckNoOptions] = useState<any>([]);
  const [truckRouteOptions, setTruckRouteOptions] = useState<any>([]);
  // 集卡作业小结
  const [truckSummaryData, setTruckSummaryData] = useState<any>([]);
  const [truckSummaryVisiable, setTruckSummaryVisiable] = useState<boolean>(false);
  const [rangeTime,setRangeTime] = useState<any>({})
  const [minHeight, setMinHeight] = useState(800);
  const [force,setForce] = useState<number>(0)

  // OEE
  const onFinish = (values: any) => {
    const startTime = moment(values?.stDt[0])?.format('YYYY/MM/DD HH:mm:ss');
    const endTime = moment(values?.stDt[1])?.format('YYYY/MM/DD HH:mm:ss');
    fetchInitData(startTime, endTime);
  };

  const fetchInitData = async (startTime: string, endTime: string) => {
    const result = await queryTpsData({
      ResponseData:{
        start_time: startTime,
        end_time: endTime,
      }
    });
    // const result = {
    //   resultData: {
    //     vessel_name_voy: ['地中海安娜', '地中海安娜1', '地中海安娜2'],
    //     truck_no: ['HT-240', 'HT-241', 'HT-242'],
    //     op_route: ['Q101', 'Q102', 'Q103'],
    //   },
    //   success: true,
    //   resultCode: '200',
    //   message: '操作成功',
    // };
    if (result && result.resultCode == '200') {
      const vesselNameOptions = result?.resultData?.vessel_name_voy?.map((vessel: any) => ({
        label: vessel,
        value: vessel,
      }));
      const truckNoOptions = result?.resultData?.truck_no?.map((truck: any) => ({
        label: truck,
        value: truck,
      }));
      const opRouteOptions = result?.resultData?.op_route?.map((route: any) => ({
        label: route,
        value: route,
      }));
      setOeeVesselNameOptions(vesselNameOptions);
      setOeeTruckNoOptions(truckNoOptions);
      setOeeRouteOptions(opRouteOptions);
      message.info('下拉数据查询成功！');
    } else {
      message.error('下拉数据查询失败！');
    }
  };

  // 查看图表详细数据
  const fetchChartsData = async (
    startTime: any,
    endTime: any,
    vesselName: any,
    opRouteList: any,
    truckNoList: any,
    type: string,
  ) => {
    const result = await fetchGenerateTpsEchartsData({
      ResponseData: {
        start_time: startTime,
        end_time: endTime,
        ves_name: vesselName || [],
        qc_id: opRouteList || [],
        ht_id: truckNoList || [],
        type: type,
      },
    });
    if (result && result?.resultCode == '200') {
      if (type == 'oee') {
        setChartOEEData(result?.resultData?.TRUCK_OEE?.detail_info);
        setChartOEEAvg(result?.resultData?.TRUCK_OEE?.average)
        setOeeVisiable(true);
      }
      if (type == 'trace') {
        setChartTruckMoveData(result?.resultData?.TRUCK_TRACE);
        setTruckTraceVisiable(true);
      }
      if (type == 'truck_summary') {
        setTruckSummaryData(result?.resultData?.TRUCK_SUMMARY);
        setTruckSummaryVisiable(true);
        setForce((pre:number)=> pre + 1)
      }
      message.info('信息查询成功！');
    } else {
      message.error('信息查询失败！');
    }
  };

  const oeeDisabledDate = (current: any) => {
    const oneYearAgo = moment().subtract(1, 'years');
    const now = moment();
    return current < oneYearAgo || current > now.endOf('day');
  };

  useEffect(() => {
    const updateHeight = () => {
      const newHeight = window.innerHeight * 0.8 - 200;
      setMinHeight(Math.max(newHeight, 800));
    };
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  return (
    <>
      <Card>
        <Tabs type="card" size={'small'} destroyInactiveTabPane={true}>
          <TabPane tab="集卡OEE分析" key={'1'}>
            <Card>
              <Form name="shipInfo" form={form} onFinish={onFinish}>
                <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                  <Col span={8}>
                    <ProFormDateTimeRangePicker
                      name="stDt"
                      label="水平机械作业时间"
                      allowClear={false}
                      fieldProps={{
                        format: 'YYYY/MM/DD HH:mm:ss',
                        style: {
                          width: '100%',
                        },
                        disabledDate: oeeDisabledDate,
                      }}
                      rules={[
                        {
                          required: true,
                          message: '',
                        },
                      ]}
                    />
                  </Col>
                  <Col span={6}>
                    <Button type="primary" htmlType="submit" style={{ marginRight: 50 }}>
                      {' '}
                      查询
                    </Button>
                    <Button
                      onClick={() => {
                        form?.resetFields(['vesselName', 'opRoute', 'truckNo']);
                      }}
                    >
                      {' '}
                      清空
                    </Button>
                  </Col>
                </Row>
                <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                  <Col span={6}>
                    <ProFormSelect
                      label="船舶"
                      name="vesselName"
                      showSearch
                      mode="multiple"
                      options={oeeVesselNameOptions}
                      fieldProps={{
                        labelInValue: false,
                        autoClearSearchValue: true,
                        style: {
                          minWidth: 100,
                        },
                      }}
                      placeholder="请选择船舶"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormSelect
                      label="桥吊编号"
                      name="opRoute"
                      showSearch
                      mode="multiple"
                      options={oeeRouteOptions}
                      fieldProps={{
                        labelInValue: false,
                        autoClearSearchValue: true,
                        style: {
                          minWidth: 100,
                        },
                      }}
                      placeholder="请选择桥吊编号"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormSelect
                      label="集卡编号"
                      name="truckNo"
                      showSearch
                      mode="multiple"
                      options={oeeTruckNoOptions}
                      fieldProps={{
                        labelInValue: false,
                        autoClearSearchValue: true,
                        style: {
                          minWidth: 100,
                        },
                      }}
                      placeholder="请选择集卡编号"
                    />
                  </Col>
                  <Col>
                    <Button
                      type="primary"
                      onClick={() => {
                        console.log('显示结果oee', form?.getFieldsValue());
                        const formValue = form?.getFieldsValue();
                        if (!formValue?.stDt) {
                          message.error('请选择时间点击查询！');
                          return;
                        }
                        const start_time = moment(formValue?.stDt?.[0])?.format(
                          'YYYY/MM/DD HH:mm:ss',
                        );
                        const end_time = moment(formValue?.stDt?.[1])?.format(
                          'YYYY/MM/DD HH:mm:ss',
                        );
                        const ves_name = formValue?.vesselName || [];
                        const qc_id = formValue?.opRoute || [];
                        const ht_id = formValue?.truckNo || [];
                        const type = 'oee';
                        fetchChartsData(start_time, end_time, ves_name, qc_id, ht_id, type);
                      }}
                    >
                      显示结果
                    </Button>
                  </Col>
                </Row>
              </Form>
              <List
                header={'集卡平均指标'}
                grid={{ gutter: 16, column: 4 }}
                dataSource={chartOEEAvg}
                renderItem={(item:any) => (
                  `${item?.cnname}：${Number(item?.value).toFixed(2)}`
                )}
              />
            </Card>
            {oeeVisiable && <AgvEfficientOeeChart truckOEEData={chartOEEData} minHeight={minHeight} />}
          </TabPane>

          <TabPane tab="集卡移动轨迹图分析" key={'2'}>
            <Card>
              <Form
                name="traceForm"
                form={traceForm}
                onFinish={async (values: any) => {
                  const startTime = moment(values?.stDt)
                    ?.startOf('day')
                    ?.format('YYYY/MM/DD HH:mm:ss');
                  const endTime = moment(values?.stDt)?.endOf('day')?.format('YYYY/MM/DD HH:mm:ss');

                  console.log('trace', startTime, endTime);
                   const result = await queryTpsData({
                    ResponseData:{
                      start_time: startTime,
                      end_time: endTime,
                    }
                  });
                  // const result = {
                  //   resultData: {
                  //     vessel_name_voy: ['地中海安娜', '地中海安娜1', '地中海安娜2'],
                  //     truck_no: ['HT-240', 'HT-241', 'HT-242'],
                  //     op_route: ['Q101', 'Q102', 'Q103'],
                  //   },
                  //   success: true,
                  //   resultCode: '200',
                  //   message: '操作成功',
                  // };
                  if (result && result.resultCode == '200') {
                    const vesselNameOptions = result?.resultData?.vessel_name_voy?.map(
                      (vessel: any) => ({
                        label: vessel,
                        value: vessel,
                      }),
                    );
                    const truckNoOptions = result?.resultData?.truck_no?.map((truck: any) => ({
                      label: truck,
                      value: truck,
                    }));
                    const opRouteOptions = result?.resultData?.op_route?.map((route: any) => ({
                      label: route,
                      value: route,
                    }));
                    setTruckVesselNameOptions(vesselNameOptions);
                    setTruckTruckNoOptions(truckNoOptions);
                    setTruckRouteOptions(opRouteOptions);
                    message.info('下拉数据查询成功！');
                  } else {
                    message.error('下拉数据查询失败！');
                  }
                }}
              >
                <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                  <Col span={8}>
                    <ProFormDatePicker
                      name="stDt"
                      label="水平机械作业时间"
                      allowClear={false}
                      fieldProps={{
                        format: 'YYYY/MM/DD',
                        style: {
                          width: '100%',
                        },
                      }}
                      rules={[
                        {
                          required: true,
                          message: '',
                        },
                      ]}
                    />
                  </Col>
                  <Col span={6}>
                    <Button type="primary" htmlType="submit" style={{ marginRight: 50 }}>
                      {' '}
                      查询
                    </Button>
                    <Button
                      onClick={() => {
                        traceForm?.resetFields(['vesselName', 'opRoute', 'truckNo']);
                      }}
                    >
                      {' '}
                      清空
                    </Button>
                  </Col>
                </Row>

                <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                  <Col span={6}>
                    <ProFormSelect
                      label="船舶"
                      name="vesselName"
                      showSearch
                      options={truckVesselNameOptions || []}
                      fieldProps={{
                        labelInValue: false,
                        autoClearSearchValue: true,
                        style: {
                          minWidth: 100,
                        },
                      }}
                      placeholder="请选择船舶"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormSelect
                      label="桥吊编号"
                      name="opRoute"
                      showSearch
                      mode="multiple"
                      options={truckRouteOptions}
                      fieldProps={{
                        labelInValue: false,
                        autoClearSearchValue: true,
                        style: {
                          minWidth: 100,
                        },
                      }}
                      placeholder="请选择桥吊编号"
                    />
                  </Col>
                  <Col span={6}>
                    <ProFormSelect
                      label="集卡编号"
                      name="truckNo"
                      showSearch
                      mode="multiple"
                      options={truckTruckNoOptions}
                      fieldProps={{
                        labelInValue: false,
                        autoClearSearchValue: true,
                        style: {
                          minWidth: 100,
                        },
                      }}
                      placeholder="请选择集卡编号"
                    />
                  </Col>
                  <Col>
                    <Button
                      type="primary"
                      onClick={() => {
                        console.log('显示结果', traceForm?.getFieldsValue());
                        const tuckFormValue = traceForm?.getFieldsValue();
                        if (!tuckFormValue?.stDt) {
                          message.error('请选择时间点击查询！');
                          return;
                        }
                        if (!tuckFormValue?.vesselName) {
                          message.error('请选择一条船后进行查询！');
                          return;
                        }
                        const startTime = moment(tuckFormValue?.stDt)
                          ?.startOf('day')
                          ?.format('YYYY/MM/DD HH:mm:ss');
                        const endTime = moment(tuckFormValue?.stDt)
                          ?.endOf('day')
                          ?.format('YYYY/MM/DD HH:mm:ss');
                        const ves_name = tuckFormValue?.vesselName || [];
                        const qc_id = tuckFormValue?.opRoute || [];
                        const ht_id = tuckFormValue?.truckNo || [];
                        const type = 'trace';
                        console.log('显示结果', startTime, endTime, [ves_name], qc_id, ht_id, type);

                        fetchChartsData(startTime, endTime, [ves_name], qc_id, ht_id, type);
                      }}
                    >
                      显示结果
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>
            {truckTraceVisiable && <TrackMovingGraph truckTraceData={chartTruckMoveData} minHeight={minHeight}/>}
          </TabPane>

          <TabPane tab="集卡作业分析小结" key={'3'}>
            <Card>
              <Form
                name="truckSummary"
                form={summaryForm}
                onFinish={async (values: any) => {
                  console.log('truckSummary', values);
                  const startTime = moment(values?.stDt[0])?.format('YYYY/MM/DD HH:mm:ss');
                  const endTime = moment(values?.stDt[1])?.format('YYYY/MM/DD HH:mm:ss');
                  setRangeTime({
                    start_time:startTime,
                    end_time:endTime
                  })
                  const type = 'truck_summary';
                  if (!values?.stDt[0]) {
                    message.error('请选择时间点击查询！');
                    return;
                  }
                  fetchChartsData(startTime, endTime, [], [], [], type);
                }}
              >
                <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                  <Col span={8}>
                    <ProFormDateTimeRangePicker
                      name="stDt"
                      label="水平机械作业时间"
                      allowClear={false}
                      fieldProps={{
                        format: 'YYYY/MM/DD HH:mm:ss',
                        style: {
                          width: '100%',
                        },
                        disabledDate: oeeDisabledDate,
                      }}
                      rules={[
                        {
                          required: true,
                          message: '',
                        },
                      ]}
                    />
                  </Col>
                  <Col span={6}>
                    <Button type="primary" htmlType="submit" style={{ marginRight: 50 }}>
                      {' '}
                      查询
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>
            {truckSummaryVisiable && <TrackJobAnalysis truckSummaryData={truckSummaryData} minHeight={minHeight} rangeTime={rangeTime} force={force}/>}
          </TabPane>
          <TabPane
          tab={'内集卡拖运量'}
          key={'4'}
          >
          <TruckTransportVolumn />
          </TabPane>
          <TabPane
           tab={'内集卡运行指标'}
           key={'5'}
          >
            <TruckIndicator />
            </TabPane>
            <TabPane
             tab={'内集卡排队分析'}
             key={'6'}
            >
            <TruckQueueAnalysis />
            </TabPane>
        </Tabs>
      </Card>
    </>
  );
};

export default ScheduleAnalysis;
