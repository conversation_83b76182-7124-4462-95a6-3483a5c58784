import { findRoleList } from '@/services/base/role';
import <PERSON>Form, { ModalForm, ProFormCheckbox, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Form, TreeSelect } from 'antd';
import { DataNode } from 'antd/es/tree';
import React, { useEffect, useState } from 'react';
import { useIntl } from 'umi';

export type CreateUserFormProps = {
  onVisibleChange?: (visible: boolean) => void;
  onSubmit: (values: API.UserCO) => Promise<void>;
  createUserFormVisible: boolean;
  values: Partial<API.UserCO>;
};

const formatAllRoles = (roles: API.RoleCO[]): any[] => {
  const formattedRoles: any[] = roles.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
  return formattedRoles;
}

const CreateUserForm: React.FC<CreateUserFormProps> = (props) => {
  const [form] = Form.useForm();

  const [roleSelectItems, setRoleSelectItems] = useState<any[]>([]);
  const intl = useIntl();

  const userUserFormat = intl.formatMessage({
    id: 'userUser',
  });
  const userLoginIdFormat = intl.formatMessage({
    id: 'userLoginId',
  });
  const userEnterLoginIdLessThan50Characters = intl.formatMessage({
    id: 'userEnterLoginIdLessThan50Characters',
  });
  const userFullnameFormat = intl.formatMessage({
    id: 'userFullname',
  });
  const userEnterFullNameLessThan50Characters = intl.formatMessage({
    id: 'userEnterFullNameLessThan50Characters',
  });
  const userTelephoneNumber = intl.formatMessage({
    id: 'userTelephoneNumber',
  });
  const userTelephoneNumberFormatIncorrect = intl.formatMessage({
    id: 'userTelephoneNumberFormatIncorrect',
  });
  const basicEmailFormat = intl.formatMessage({
    id: 'basicEmail',
  });
  const userEmailFormatIncorrect = intl.formatMessage({
    id: 'userEmailFormatIncorrect',
  });
  const userActivatedorNot = intl.formatMessage({
    id: 'userActivatedorNot',
  });

  useEffect(() => {
    findRoleList({}).then((res) => {
      const formattedRoles = formatAllRoles(res.data ?? []);
      setRoleSelectItems(formattedRoles);
    });
  }, []);

  return (
    <ModalForm
      form={form}
      title={userUserFormat}
      visible={props.createUserFormVisible}
      onFinish={props.onSubmit}
      onVisibleChange={props.onVisibleChange}
    >
      <ProForm.Group>
        <ProFormText
          name="name"
          label={userLoginIdFormat}
          width="md"
          allowClear={false}
          rules={[
            {
              required: true,
              message: userEnterLoginIdLessThan50Characters,
              max: 50,
            },
          ]}
        />
        <ProFormText
          name="fullName"
          label={userFullnameFormat}
          width="md"
          allowClear={false}
          rules={[
            {
              required: true,
              message: userEnterFullNameLessThan50Characters,
              max: 50,
            },
          ]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="phoneNumber"
          label={userTelephoneNumber}
          rules={[
            {
              pattern: /^\d{11}$/,
              message: userTelephoneNumberFormatIncorrect,
            },
          ]}
        />
        <ProFormText
          width="md"
          name="email"
          label={basicEmailFormat}
          rules={[
            {
              type: 'email',
              message: userEmailFormatIncorrect,
            },
          ]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          name="roleIds"
          label="角色"
          width="md"
          options={roleSelectItems}
          mode="multiple"
          rules={[{ required: true, message: '请选择角色!' }]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormCheckbox
          name="enabled"
          label={userActivatedorNot}
          width="md"
          allowClear={false}
          initialValue={true}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default CreateUserForm;
