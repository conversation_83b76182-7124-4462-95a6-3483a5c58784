/*
 * @LastEditors: xiao-huahua
 * @Description:
 * @Author: sr
 * @Date: 2023-12-19 09:23:25
 */
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import {useIntl} from "umi";
import React from 'react';

export type CreateRoleFormProps = {
  onVisibleChange?: (visible: boolean) => void;
  onSubmit: (values: API.RoleCO) => Promise<void>;
  createRoleFormVisible: boolean;
  values: Partial<API.RoleCO>;
};

const CreateRoleForm: React.FC<CreateRoleFormProps> = (props) => {
  const intl = useIntl();
  const characterCharacterAddition = intl.formatMessage({
    id: 'characterCharacterAddition',
  });
  const characterCodeTwo = intl.formatMessage({
    id: 'characterCodeTwo',
  });
  const characterEnterCodeLessThan50Characters = intl.formatMessage({
    id: 'characterEnterCodeLessThan50Characters',
  });
  const characterName = intl.formatMessage({
    id: 'characterName',
  });
  const characterEnterNameLessThan50Characters = intl.formatMessage({
    id: 'characterEnterNameLessThan50Characters',
  });

  return (
    <ModalForm
      title={characterCharacterAddition}
      width="400px"
      open={props.createRoleFormVisible}
      onFinish={props.onSubmit}
      onOpenChange={props.onVisibleChange}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormText
        name="code"
        label={characterCodeTwo}
        width="md"
        allowClear={true}
        rules={[
          {
            required: true,
            message: characterEnterCodeLessThan50Characters,
            max: 50,
          },
        ]}
      />
      <ProFormText
        name="name"
        label={characterName}
        width="md"
        allowClear={true}
        rules={[
          {
            required: true,
            message: characterEnterNameLessThan50Characters,
            max: 50,
          },
        ]}
      />
    </ModalForm>
  );
};

export default CreateRoleForm;
