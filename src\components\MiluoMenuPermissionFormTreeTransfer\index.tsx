/*
 * @Description:
 * @Version: 2.0
 * @Author: huzhenghui
 * @Date: 2021-08-18 22:10:26
 * @LastEditors: sr
 * @LastEditTime: 2023-12-29 15:42:10
 */
import { CheckCircleOutlined, FileOutlined } from '@ant-design/icons';
import { Transfer, Tree } from 'antd';
import React, { useState } from 'react';

const isChecked = function isChecked(selectedKeys: number[], eventKey: number): boolean {
  return selectedKeys.indexOf(eventKey) !== -1;
};

const getIcon = (type?: number) => {
  switch (type) {
    case 1:
      return <FileOutlined />;
    case 2:
      return <CheckCircleOutlined />;
    default:
      break;
  }
};

const removeSelectedNode = (completeTree: any[], checkedKeys: number[]): any[] => {
  const newTree: any[] = [];
  completeTree.forEach((item) => {
    const node = { ...item };
    let childrenList = [];
    if (node.children) {
      childrenList = removeSelectedNode(node.children, checkedKeys);
    }
    if (checkedKeys.includes(node.key)) {
      node.children = [...childrenList];
      newTree.push(node);
    }
    if (node.type === 0 && childrenList.length > 0) {
      node.children = [...childrenList];
      newTree.push(node);
    }
  });
  return newTree;
};

const findParentKey = (
  parentKey: number,
  menuListData: MenuPermissionTreeData[],
  keyList: number[],
): void => {
  const parent: MenuPermissionTreeData | undefined = menuListData.find(
    (item) => item.permissionId === parentKey,
  );
  if (!parent) return;
  if (parent.type !== 0 && parent.permissionId) {
    keyList.push(parent.permissionId);
  }
  if (parent.parenPermissionId) {
    findParentKey(parent.parenPermissionId, menuListData, keyList);
  }
};

const autoSelectParentNode = (
  childKey: number,
  menuListData: MenuPermissionTreeData[],
  cb: (keys: number[]) => void,
): void => {
  const keyList: number[] = [];
  keyList.push(childKey);
  const child: MenuPermissionTreeData | undefined = menuListData.find(
    (item) => item.permissionId === childKey,
  );
  if (child && child.parenPermissionId) {
    findParentKey(child.parenPermissionId, menuListData, keyList);
  }
  cb(keyList);
};

const findTreeNode = (
  key: number,
  treeData: MenuPermissionTreeData[],
): MenuPermissionTreeData | undefined => {
  const node: MenuPermissionTreeData | undefined = treeData.find(
    (item) => item.permissionId === key,
  );
  if (node) return node;
  let children: MenuPermissionTreeData[] = [];
  treeData.forEach((item) => {
    if (item.children) children = [...children, ...item.children];
  });
  if (children.length === 0) return undefined;
  return findTreeNode(key, children);
};

const getMenuKeyList = (menuTreeData: MenuPermissionTreeData[]): number[] => {
  let keyList: number[] = [];
  menuTreeData.forEach((item) => {
    if (item.children && item.children.length > 0) {
      const childrenList = getMenuKeyList(item.children);
      keyList = [...keyList, ...childrenList];
    }
    keyList.push(item.permissionId);
  });
  return keyList;
};

const autoSelectChildrenNode = (
  parentKey: number,
  rightTreeNode: MenuPermissionTreeData[],
  cb: (keys: number[]) => void,
): void => {
  let keyList: number[] = [];
  const current: MenuPermissionTreeData | undefined = findTreeNode(parentKey, rightTreeNode);
  if (current) {
    keyList = [...keyList, ...getMenuKeyList([current])];
  }
  cb(keyList);
};

export type MenuPermissionTreeData = {
  permissionId: number;
  parenPermissionId: number;
  title?: string;
  key?: string;
  type?: number;
  code: string;
  checkable?: boolean;
  parentCode?: string;
  children?: MenuPermissionTreeData[];
};

export type MiluoMenuPermissionFormTreeTransferProps = {
  dataSource: MenuPermissionTreeData[];
  menuListData: MenuPermissionTreeData[];
  value?: any[];
  onChange?: (keys: number[]) => void;
};

const MiluoMenuPermissionFormTreeTransfer: React.FC<MiluoMenuPermissionFormTreeTransferProps> = ({
  dataSource,
  menuListData,
  value,
  onChange,
  ...restProps
}) => {
  const [rightCheckKeys, setRightCheckedKeys] = useState<number[]>([]);
  const [leftSearchValue, setLeftSearchValue] = useState<string>();
  const [rightSearchValue, setRightSearchValue] = useState<string>();

  const transferDataSource: MenuPermissionTreeData[] = [];
  function flatten(list: any[]) {
    list.forEach((item) => {
      transferDataSource.push(item);
      if (item.children) flatten(item.children);
    });
  }
  flatten(dataSource);

  const selfChange = (keys: number[], direction: string) => {
    if (onChange) onChange(keys);
    if (direction === 'left') {
      setRightCheckedKeys([]);
    }
  };

  const modifyRightCheckedKeys = (keys: number[], selected: boolean) => {
    if (selected) {
      setRightCheckedKeys([...rightCheckKeys, ...keys]);
    } else {
      const newRightKeys: number[] = [];
      rightCheckKeys.forEach((item) => {
        if (keys.findIndex((key) => key === item) < 0) {
          newRightKeys.push(item);
        }
      });
      setRightCheckedKeys([...newRightKeys]);
    }
  };
  const values = value || [];

  const getTitle = (node: any, isLeft: boolean) => {
    let matchValue = rightSearchValue;
    if (isLeft) {
      matchValue = leftSearchValue;
    }
    return node.parentCode && matchValue && node?.title.indexOf(matchValue) !== -1 ? (
      <span style={{ backgroundColor: 'yellow' }}>{node.title}</span>
    ) : (
      node.title
    );
  };

  const generateTree = (treeNodes: MenuPermissionTreeData[], checkedKeys: number[]): any[] => {
    return treeNodes.map(({ children, key, ...props }) => {
      return {
        ...props,
        key,
        title: getTitle(props, false),
        icon: getIcon(props.type),
        checkable: props.type !== 0,
        children: children ? generateTree(children, checkedKeys) : [],
      };
    });
  };

  const generateRightTree = (treeNodes: MenuPermissionTreeData[], checkedKeys: number[]): any[] => {
    if (!checkedKeys.length) {
      return [];
    }
    const completeTree = generateTree(treeNodes, checkedKeys);
    const finalTree = removeSelectedNode(completeTree, checkedKeys);
    return finalTree;
  };

  const generateLeftTree = (treeNodes: MenuPermissionTreeData[], checkedKeys: number[]): any[] => {
    return treeNodes
      .map(({ children, ...props }) => ({
        ...props,
        checkable: props.type !== 0,
        title: getTitle(props, true),
        icon: getIcon(props.type),
        disabled: props.key ? checkedKeys.includes(props.key) : true,
        children: children ? generateLeftTree(children, checkedKeys) : [],
      }))
      ?.filter(
        (item) =>
          (!checkedKeys.includes(item?.key) && item.parentCode) || item?.children?.length !== 0,
      );
  };

  const leftTreeNode: any[] = generateLeftTree(dataSource, values);
  const rightTreeNode: any[] = generateRightTree(dataSource, values);

  const filterOption = (inputValue: string, option: RecordType) =>
    option.title.indexOf(inputValue) > -1;

  return (
    <Transfer
      {...restProps}
      titles={['未拥有权限', '已拥有权限']}
      operations={['赋权', '移除']}
      onChange={selfChange}
      targetKeys={value}
      dataSource={transferDataSource?.filter((item) => item.parentCode)}
      className="tree-transfer"
      showSelectAll={false}
      showSearch
      filterOption={filterOption}
      onSearch={(dir, val) => {
        if (dir === 'left') {
          setLeftSearchValue(val);
        } else {
          setRightSearchValue(val);
        }
      }}
    >
      {({ direction, selectedKeys, onItemSelectAll }) => {
        const checkedKeys = [...selectedKeys, ...values];
        if (direction === 'left') {
          return (
            <>
              {leftTreeNode.length > 0 && (
                <Tree
                  showIcon
                  blockNode
                  checkable
                  checkStrictly
                  defaultExpandAll
                  checkedKeys={checkedKeys}
                  treeData={leftTreeNode}
                  onCheck={(_, { node: { key } }) => {
                    autoSelectParentNode(key, menuListData, (keyn: number[]): void => {
                      onItemSelectAll(keyn, !isChecked(checkedKeys, key));
                    });
                  }}
                  onSelect={(_, { node }: any) => {
                    if (node.type === 0) return;
                    autoSelectParentNode(node.key, menuListData, (key: number[]): void => {
                      onItemSelectAll(key, !isChecked(checkedKeys, node.key));
                    });
                  }}
                />
              )}
            </>
          );
        }
        return (
          <>
            {rightTreeNode.length > 0 && (
              <Tree
                showIcon
                blockNode
                checkable
                checkStrictly
                defaultExpandAll
                checkedKeys={rightCheckKeys}
                treeData={rightTreeNode}
                onCheck={(_, { node: { key } }) => {
                  autoSelectChildrenNode(key, rightTreeNode, (keyn: number[]): void => {
                    onItemSelectAll(keyn, !isChecked(rightCheckKeys, key));
                    modifyRightCheckedKeys(keyn, !isChecked(rightCheckKeys, key));
                  });
                }}
                onSelect={(_, { node }: any) => {
                  if (node.type === 0) return;
                  autoSelectChildrenNode(node.key, rightTreeNode, (keyn: number[]): void => {
                    onItemSelectAll(keyn, !isChecked(rightCheckKeys, node.key));
                    modifyRightCheckedKeys(keyn, !isChecked(rightCheckKeys, node.key));
                  });
                }}
              />
            )}
          </>
        );
      }}
    </Transfer>
  );
};

export default MiluoMenuPermissionFormTreeTransfer;
