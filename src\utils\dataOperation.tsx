/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @version: 2.0
 * @Date: 2021-10-28 11:05:18
 * @LastEditors: Wangshideng
 * @LastEditTime: 2022-01-20 10:34:14
 * @Descripttion: 
 */
//除法
export function accDiv(arg1:any,arg2:any){
    var t1=0,t2=0,r1,r2;
    try{t1=arg1.toString().split(".")[1].length}catch(e){}
    try{t2=arg2.toString().split(".")[1].length}catch(e){}
    while(Math){
        r1=Number(arg1.toString().replace(".",""))
        r2=Number(arg2.toString().replace(".",""))
        return Number(accMul((r1/r2),Math.pow(10,t2-t1)));
    }
    return 0;
}

//乘法
export function accMul(arg1:any,arg2:any){
    var m=0,s1=arg1.toString(),s2=arg2.toString();
    try{m+=s1.split(".")[1].length}catch(e){}
    try{m+=s2.split(".")[1].length}catch(e){}
    return Number(s1.replace(".","")) * Number(s2.replace(".",""))/Math.pow(10,m)
}

//加法
export function accAdd(arg1:any,arg2:any){
    var r1,r2,m;
    try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
    try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
    m=Math.pow(10,Math.max(r1,r2))
    return Number((accMul(arg1, m)+accMul(arg2, m))/m)
}

//减法
export function subtr(arg1:any,arg2:any){
    var r1,r2,m,n;
    try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
    try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
    m=Math.pow(10,Math.max(r1,r2));
    n=(r1>=r2)?r1:r2;
    return Number(((accMul(arg1, m)-accMul(arg2, m))/m).toFixed(n));
}