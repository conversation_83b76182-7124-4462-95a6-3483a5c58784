pipeline {
    agent any
    environment {
        DOCKER_URL = 'registry.cn-shanghai.aliyuncs.com/gyzqbx'
        DOCKER_NAME = 'tps-monitor-ui'
        PORT = 8888
        GRAFANA_URL = 'http://***********:3000'
     }
    parameters {
        string(name: 'CLIENT_NAME', defaultValue: 'miluo', description: '客户名称')
        string(name: 'ENV', defaultValue: 'test', description: '环境')
    }
    stages {
        stage('Docker Build') {
            agent any
            steps {
                script {
                    sh '''
                        git checkout tops
                        git pull
                        git branch
                    '''
                    sh "sh addVersion.sh"
                    version = sh (returnStdout: true ,script: "cat version.txt").trim()
                    echo "版本: ${version}"

                    sh "docker build --build-arg ENV=${ENV} --tag ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version} ."
                    sh "docker push ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version}"

                    sh '''
                    git add .
                    git commit -a -m "refactor : 修改版本号-from:jenkins"
                    git push -f origin tops
                    '''
                    try {
                        sh "docker rmi -f ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version}"
                    } catch (err) {
                        echo 'clean none image error'
                    }
                }
            }
        }
        stage('Container Create') {
            agent any
            steps {
                sh "ssh root@*********** docker pull ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version}"
                script {
                    try {
                        sh "ssh root@*********** docker rm -f ${DOCKER_NAME}"
                    } catch (err) {
                        echo 'clean none container error'
                    }
                }
                sh "ssh root@*********** docker run -d --name ${DOCKER_NAME} -p ${PORT}:8000 -e GRAFANA_URL=${GRAFANA_URL} ${DOCKER_URL}/${DOCKER_NAME}:${CLIENT_NAME}-${ENV}-${version}"
            }
        }
    }
}
