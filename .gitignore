<<<<<<< HEAD
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist

# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
package-lock.json
*bak
.vscode


# visual studio code
.history
*.log
functions/*
.temp/**

# umi
.umi
.umi-production
.umi-test

# screenshot
screenshot
.firebase
.eslintcache

build
=======
# Build and Release Folders
bin-debug/
bin-release/
[Oo]bj/
[Bb]in/

# Other files and folders
.settings/

# Executables
*.swf
*.air
*.ipa
*.apk

# Project files, i.e. `.project`, `.actionScriptProperties` and `.flexProperties`
# should NOT be excluded as they contain compiler settings and other important
# information for Eclipse / Flash Builder.
>>>>>>> f0af0a832dd59b445d3cb3d57a3d5bf479c23eda
