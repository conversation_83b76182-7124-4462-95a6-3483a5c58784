/*
 * @Descripttion: 
 * @Author: miluo-刘书记
 * @Date: 2024-07-11 10:22:34
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-09-20 15:05:05
 */
import { useCallback } from 'react';
import * as XLSX from 'xlsx';

const useExcelExport =  () => {
  const exportToExcel = useCallback((data:any, fileName:any) => {
    // 动态设置列宽
    const columnWidths = data?.map(()=>({wch:15}))
    const worksheet = XLSX?.utils?.json_to_sheet(data);
    worksheet['!cols'] = columnWidths;
    const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    const excelBuffer = XLSX?.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.xlsx`;
    a.click();
    window.URL.revokeObjectURL(url);
  }, []);

  return exportToExcel;
};

export default useExcelExport;
