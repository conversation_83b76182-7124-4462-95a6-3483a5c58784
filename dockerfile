FROM node:16.14.0 as builder
WORKDIR /app
COPY ["package.json", "./"]
COPY ["pnpm-lock.yaml", "./"]
RUN npm i -g pnpm@8.6.12

RUN pnpm config set registry https://registry.npmmirror.com
RUN pnpm get registry

RUN pnpm i
COPY . .
ARG ENV
RUN pnpm build:${ENV}

FROM nginx:latest
ARG ENV
COPY /nginx/${ENV}/nginx.conf /etc/nginx
COPY --from=builder /app/dist /usr/share/nginx/html

COPY /ssl/hb.crt /etc/nginx/ssl/hb.crt
COPY /ssl/hb.key /etc/nginx/ssl/hb.key

COPY /replace_url.sh /
CMD ["sh", "replace_url.sh"]

EXPOSE 8000
EXPOSE 443
