/*
 * @Author: <PERSON><PERSON>den<PERSON>
 * @version: 2.0
 * @Date: 2022-01-10 17:53:01
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-07-05 09:54:32
 * @Description:
 */
import { ProFormText } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import React from 'react';
import { useIntl } from 'umi';

const ChangePasswordForm: React.FC<any> = () => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const intl = useIntl();

  const currentPasswordFormat = intl.formatMessage({
    id: 'currentPassword',
  });
  const newPasswordFormat = intl.formatMessage({
    id: 'newPassword',
  });
  const confirmPasswordFormat = intl.formatMessage({
    id: 'confirmPassword',
  });
  const enterPassword = intl.formatMessage({
    id: 'enterPassword',
  });
  const passwordIncorrect = intl.formatMessage({
    id: 'passwordIncorrect',
  });
  const userInformationIsInvalid = intl.formatMessage({
    id: 'enterPassword',
  });
  const pleaseEnterNewPassword = intl.formatMessage({
    id: 'pleaseEnterNewPassword',
  });
  const cannotBeThePreviousPassword = intl.formatMessage({
    id: 'cannotBeThePreviousPassword',
  });
  const pleaseConfirmNewPassword = intl.formatMessage({
    id: 'pleaseConfirmNewPassword',
  });
  const enteredPasswordsDiffer = intl.formatMessage({
    id: 'enteredPasswordsDiffer',
  });

  return (
    <>
      <ProFormText.Password
        name="oldPassword"
        label={currentPasswordFormat}
        width="md"
        rules={[
          {
            required: true,
            message: enterPassword,
          },
          // () => ({
          //   async validator(_, value) {
          //     if (value && currentUser?.id) {
          //       const data = await verifyUserPassword({ id: currentUser.id, password: value });
          //       if (data.success) {
          //         return Promise.resolve();
          //       }
          //       return Promise.reject(new Error(passwordIncorrect));
          //     }
          //     return Promise.reject(new Error(userInformationIsInvalid));
          //   },
          // }),
        ]}
      />
      <ProFormText.Password
        name="newPassword"
        label={newPasswordFormat}
        width="md"
        rules={[
          {
            required: true,
            message: pleaseEnterNewPassword,
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('oldPassword') !== value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(cannotBeThePreviousPassword));
            },
          }),
        ]}
      />
      <ProFormText.Password
        name="repeatNewPassword"
        label={confirmPasswordFormat}
        width="md"
        dependencies={['newPassword']}
        rules={[
          {
            required: true,
            message: pleaseConfirmNewPassword,
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              if (!value || getFieldValue('oldPassword') === value) {
                return Promise.reject(new Error(cannotBeThePreviousPassword));
              }
              return Promise.reject(new Error(enteredPasswordsDiffer));
            },
          }),
        ]}
      />
    </>
  );
};

export default ChangePasswordForm;
