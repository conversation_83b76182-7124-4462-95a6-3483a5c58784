import { MenuUnfoldOutlined, ProfileOutlined, TableOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { List, Tree } from 'antd';
import { BasicDataNode, DataNode } from 'antd/es/tree';
import React, { useEffect, useState } from 'react';

type PermissionDatasViewProps = {
  modalOpen: boolean;
  handleModalOpen?: (modalOpen: boolean) => void;
  groupPermissionDatas: API.PermissionDataCO[];
};

const treeIcons: React.ReactElement[] = [
  // eslint-disable-next-line react/jsx-key
  <MenuUnfoldOutlined />,
  // eslint-disable-next-line react/jsx-key
  <ProfileOutlined />,
  // eslint-disable-next-line react/jsx-key
  <TableOutlined />,
];

const PermissionDatasView: React.FC<PermissionDatasViewProps> = (props) => {

    const renderItemIcon = (type: S) => {
        switch (type) {
          case 1:
            return <ProfileOutlined />;
          case 2:
            return <TableOutlined />;
          default:
            return null;
        }
      };

  return (
    <ModalForm
      title={`数据权限预览`}
      width="420px"
      style={{ padding: '12px 12px 12px' }}
      open={props.modalOpen}
      onOpenChange={props.handleModalOpen}
      onFinish={async () => {
        props?.handleModalOpen?.(false);
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
        <List
        itemLayout="vertical" // 设置 itemLayout 为 "vertical"
        dataSource={props.groupPermissionDatas}
        renderItem={(item: API.PermissionDataCO) => (
          <List.Item>
            <ProfileOutlined /> {/* 使用固定的图标元素 */}
            <span>{item.code}-{item.description}</span>
          </List.Item>
        )}
      />
    </ModalForm>
  );
};

export default PermissionDatasView;
