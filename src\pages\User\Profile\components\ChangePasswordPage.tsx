/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @version: 2.0
 * @Date: 2022-01-10 17:53:01
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-07-05 09:55:00
 * @Descripttion:
 */
import { changePassword, verifyUserPassword } from '@/services/base/user';
import { PageContainer } from '@ant-design/pro-components';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import React, { useRef } from 'react';
import { useIntl } from 'umi';
import ChangePasswordForm from './ChangePasswordForm';

const ChangePasswordPage: React.FC<any> = () => {
  const formRef = useRef<ProFormInstance>();

  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const intl = useIntl();

  const securityEditSuccess = intl.formatMessage({
    id: 'securityEditSuccess',
  });
  const systemError = intl.formatMessage({
    id: 'systemError',
  });
  const onSubmit = async (value: API.ChangeUserPasswordCO) => {
    try {
      const response = await changePassword({
        changeUserPasswordCO: value,
      });
      if (response.success) {
        formRef.current?.resetFields();
        message.success(securityEditSuccess);
      } else {
        message.error(response.message);
      }
    } catch {
      message.error(systemError);
    }
  };
  const verifyUserCurrentPassword = async (values: any) => {
    if (values && currentUser?.id) {
      const data = await verifyUserPassword({ id: currentUser.id, password: values?.oldPassword });
      if (data.success) {
        return true;
      } else {
        message.error('The Current Password is Incorrect !');
        return false;
      }
    }
  };
  return (
    <PageContainer
      header={{
        title: null,
        breadcrumb: {},
      }}
    >
      <ProForm
        formRef={formRef}
        onFinish={async (value) => {
          const changeUserPasswordCO = value as API.ChangeUserPasswordCO;
          changeUserPasswordCO.id = currentUser?.id ?? 0;
          const flag = await verifyUserCurrentPassword(value); // 校验当前密码
          if (flag) {
            onSubmit(changeUserPasswordCO);
          }
        }}
        submitter={{
          resetButtonProps: {
            style: {
              display: 'none',
            },
          },
        }}
        initialValues={{
          oldPassword: '',
          newPassword: '',
        }}
      >
        {<ChangePasswordForm />}
      </ProForm>
    </PageContainer>
  );
};

export default ChangePasswordPage;
