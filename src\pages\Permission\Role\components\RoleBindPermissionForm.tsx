/*
 * @Description:
 * @Version: 2.0
 * @Author: huz<PERSON>gh<PERSON>
 * @Date: 2021-08-18 17:52:43
 * @LastEditors: xiao-huahua
 * @LastEditTime: 2024-07-23 16:49:51
 */
import type { MenuPermissionTreeData } from '@/components/MiluoMenuPermissionFormTreeTransfer';
import MiluoMenuPermissionFormTreeTransfer from '@/components/MiluoMenuPermissionFormTreeTransfer';
import { findMenuList } from '@/services/base/role';
import { ModalForm } from '@ant-design/pro-form';
import { useMount } from 'ahooks';
import {useIntl} from 'umi';
import { Form } from 'antd';
import React, { useState } from 'react';

export type RoleBindPermissionFormProps = {
  onVisibleChange?: (visible: boolean) => void;
  onSubmit: (values: any) => Promise<void>;
  onCancel: (flag?: boolean) => void;
  roleBindPermissionFormVisible: boolean;
  values: any;
  // values: Partial<API.RoleCO>;
};

const RoleBindPermissionForm: React.FC<RoleBindPermissionFormProps> = (props) => {
  const [menuTreeData, setMenuTreeData] = useState<MenuPermissionTreeData[]>([]);
  const [menuListData, setMenuListData] = useState<MenuPermissionTreeData[]>([]);
  const intl = useIntl();
  const characterBindingAuthority = intl.formatMessage({
    id: 'characterBindingAuthority',
  });
  const characterButtonAuthority = intl.formatMessage({
    id: 'characterButtonAuthority',
  });

  const getButtonPermissionFormNodeData = (
  permissionButtonList: API.PermissionCO[],
): MenuPermissionTreeData[] => {
  return permissionButtonList.map((item) => ({
    key: item.code,
    title: `[${characterButtonAuthority}]-${item.name}`,
    code: item.code || '',
    checkable: true,
    type: 'BUTTON',
  }));
};

const getNenuPermissionFormTreeData = (menuData: API.MenuUICO[]): MenuPermissionTreeData[] => {
  const treeData: MenuPermissionTreeData[] = [];
  menuData.forEach((item) => {
    let children: MenuPermissionTreeData[] = [];
    if (item.children && item.children.length > 0) {
      const childrenList = getNenuPermissionFormTreeData(item.children);
      children = [...childrenList];
    }
    if (item.permissionUIList && item.permissionUIList.length > 0) {
      const buttonList = getButtonPermissionFormNodeData(item.permissionUIList);
      children = [...children, ...buttonList];
    }
    const newChildren: MenuPermissionTreeData[] = [];
    children.forEach((child) => {
      const newChild: MenuPermissionTreeData = { ...child, parentCode: item.code };
      newChildren.push(newChild);
    });
    const treeNodeData: MenuPermissionTreeData = {
      ...item,
      key: item.permissionId,
      title: item.name,
      checkable: true,
      children: [...newChildren],
    };
    treeData.push(treeNodeData);
  });
  return treeData;
};

const getMenuListData = (menuTreeData: MenuPermissionTreeData[]): MenuPermissionTreeData[] => {
  let menuListData: MenuPermissionTreeData[] = [];
  menuTreeData.forEach((item) => {
    if (item.children && item.children.length > 0) {
      const childrenList = getMenuListData(item.children);
      menuListData = [...menuListData, ...childrenList];
    }
    menuListData.push(item);
  });
  return menuListData;
};

  useMount(async () => {
    const res: API.MultiApiResultMenuUICO = await findMenuList();
    if (!res) return;
    const menuData: API.MenuUICO[] = res?.data || [];
    const remoteMenuTreeData = getNenuPermissionFormTreeData(
      menuData?.filter((item) => item.permissionId || item?.children?.length > 0),
    );
    setMenuTreeData(remoteMenuTreeData);
    setMenuListData(getMenuListData(remoteMenuTreeData));
  });

  const { values } = props;

  return (
    <ModalForm
      title={`${characterBindingAuthority}:${values.role.name}-${values.role.code}`}
      width="1000px"
      open={props.roleBindPermissionFormVisible}
      onFinish={props.onSubmit}
      onOpenChange={props.onVisibleChange}
      initialValues={{
        permissions: props.values.permissions,
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <Form.Item name="permissions">
        <MiluoMenuPermissionFormTreeTransfer
          dataSource={menuTreeData}
          menuListData={menuListData}
        />
      </Form.Item>
    </ModalForm>
  );
};

export default RoleBindPermissionForm;
