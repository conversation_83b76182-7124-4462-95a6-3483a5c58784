import { Access, useAccess } from '@umijs/max';
import { PropsWithChildren } from 'react';

export interface UIAccessProps {
  fallback?: React.ReactNode;
  access: string;
}

export const UIAccess: React.FC<PropsWithChildren<UIAccessProps>> = (props) => {
  const access = useAccess();
  const accessCode = props.access;
  const canAccessUI = access['canAccessUI'];

  //TODO 后续支持props.access为函数类型
  let canAccess = false;
  if (typeof canAccessUI === 'function') {
    canAccess = canAccessUI(accessCode);
  }

  return (
    <Access accessible={!!canAccess} fallback={props.fallback ?? <></>}>
      {props.children}
    </Access>
  );
};
