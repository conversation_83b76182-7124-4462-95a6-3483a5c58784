/*
 * @Descripttion: 
 * @Author: miluo-刘书记
 * @Date: 2024-01-10 09:17:47
 * @LastEditors: sr
 * @LastEditTime: 2024-08-13 10:54:24
 */
import { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // 拂晓蓝
  colorPrimary: '#1890ff',
  layout: 'top',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: '码头集卡调度分析监控系统(TPS)',
  pwa: true,
  // logo: '/logo.png',
  splitMenus: false,
  iconfontUrl: '',
  token: {
    // 参见ts声明，demo 见文档，通过token 修改样式
    //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F
  },
};

export default Settings;
