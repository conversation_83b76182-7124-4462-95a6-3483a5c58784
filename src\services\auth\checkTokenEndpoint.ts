// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 此处后端没有提供注释 GET /oauth/check_token */
export async function checkToken6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkToken6Params,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`/auth/oauth/check_token`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /oauth/check_token */
export async function checkToken(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkTokenParams,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`/auth/oauth/check_token`, {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /oauth/check_token */
export async function checkToken5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkToken5Params,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`/auth/oauth/check_token`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /oauth/check_token */
export async function checkToken2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkToken2Params,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`/auth/oauth/check_token`, {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PATCH /oauth/check_token */
export async function checkToken3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.checkToken3Params,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`/auth/oauth/check_token`, {
    method: 'PATCH',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
