export default {
  //拦截器
  interfaceError: '接口异常',

  // 用户管理界面
  userLoginId: '登录名',
  userUserName: '用户名',
  userProfilePhoto: '头像',
  userTelephoneNumber: '电话号码',
  userDepartment: '所属部门',
  userStatus: '状态',
  userLastLoginTime: '最近登陆时间',
  userModificationTime: '修改时间',
  userOptions: '操作',
  userEdit: '编辑',
  userDisable: '禁用',
  userEnable: '启用',
  userResetPassword: '重置密码',
  userPreviewAuthority: '预览权限',
  basicEmail: '邮箱',
  userDisableUserGroup: '确认禁用该用户吗？',
  userDisableUserGroupSuccessful: '禁用用户成功',
  userDisableUserGroupFailure: '禁用用户失败',
  userManagement: '用户管理',
  userRecreateConfirmation: '确认激活该用户吗？',
  userActiveSuccessful: '激活用户成功',
  userActiveFailure: '激活用户失败',
  userPasswordRecreateConfirmation: '确认重置该用户的密码吗？',
  userSelected: '已选择',
  userItem: '项',
  userAddition: '新建',
  userExport: '导出',

  userAddingUser: '正在添加用户',
  userAdditionSuccessful: '添加新用户成功',
  userAdditionFailure: '添加新用户失败',
  userModifyingUser: '正在修改用户',
  userUserModificationSuccessful: '修改用户成功',
  userUserModificationFailure: '修改用户失败',
  userResetingPassword: '正在重置密码',
  userPasswordSuccessfullyResetInitialPassword: '已成功将该用户密码重置为初始密码',
  userResetingPasswordFailure: '重置密码失败',
  userUser: '用户',
  userEnterLoginIdLessThan50Characters: '请输入少于50个字符登录名！',
  userFullname: '姓名',
  userEnterFullNameLessThan50Characters: '请输入少于50个字符姓名！',
  userTelephoneNumberFormatIncorrect: '手机号格式错误',
  userEmailFormatIncorrect: '邮箱地址格式错误',
  userDepartmentTwo: '部门',
  userUserGroup: '用户组',
  userActivatedorNot: '是否激活',
  userPleaseEnter: '请输入',

  //部门职位管理
  departmentCode: '编码',
  departmentName: '名称',
  departmentStatus: '状态',
  departmentCreationTime: '创建时间',
  departmentModificationUser: '修改用户',
  departmentModificationTime: '修改时间',
  departmentOptions: '操作',
  departmentEdit: '编辑',
  departmentPreviewAuthority: '预览权限',
  departmentPositionManagement: '部门职位管理',
  departmentAddition: '新建部门',
  sequence: '顺序',
  availabilityornot: '是否可用',
  role: '角色',
  pleaseLessThan: '请输入少于50个字符！',
  selectedRole: '已选角色',
  // 用户组管理
  userGroupName: '用户组名称',
  userGroupCode: '用户组编码',
  userGroupStatus: '状态',
  userGroupDepartment: '所属部门',
  userGroupOptions: '操作',
  userGroupEdit: '编辑',
  userGroupDisableUserGroup: '确认禁用该用户组吗？',
  userGroupDisableUserGroupSuccessful: '禁用用户组成功',
  userGroupDisableUserGroupFailure: '禁用用户组失败',
  userGroupDisableUserGroupConfirm: '是',
  userGroupDisableUserGroupCancel: '否',
  userGroupEnableUserGroup: '确认激活该用户组吗',
  userGroupEnableUserGroupSuccessful: '激活用户组成功',
  userGroupEnableUserGroupFailure: '激活用户组失败',
  userGroupEnableUserGroupConfirm: '是',
  userGroupEnableUserGroupCancel: '否',
  userGroupPreviewAuthority: '预览权限',
  userGroupDisable: '禁用',
  userGroupEnable: '激活',
  userGroupManagement: '用户组管理',

  userGroupAddition: '新建用户组成功',
  userGroupAdditionError: '新建用户组发生异常',
  userGroupModifyUserGroupSuccessful: '修改用户组成功',
  userGroupModifyUserGroupError: '修改用户组发生异常',
  'userGroupPreviewInterfaceAuthority ': '界面权限预览',
  userGroupPreviewDataAuthority: '预览数据权限',
  userGroupUserGroup: '用户组',
  userGroupEnterUserGroupCode: '请输入用户组编码',
  userGroupEnterUserGroupName: '请输入用户组名称',
  userGroupPleaseSelectDepartment: '请选择部门',
  'userGroupCharacter ': '角色',
  userGroupCharacterList: '角色列表',
  userGroupCharacterAdded: '已添加角色',
  userGroupAdd: '添加',
  userGroupRemove: '移除',

  // 角色管理
  characterCode: '编码',
  characterName: '角色名',
  characterDepartment: '所属部门',
  characterCreationTime: '创建时间',
  characterModificationUser: '修改用户',
  characterModificationTime: '修改时间',
  characterOptions: '操作',
  characterBindingAuthority: '绑定权限',
  characterBindingDataAuthority: '绑定数据权限',
  characterEdit: '编辑',
  characterDelete: '删除',
  characterManagement: '角色管理',

  characterConfirmDeleteCharacter: '确认删除该角色吗？',
  characterDeleteCharactersInBatches: '确认批量删除选中角色吗？',
  characterBatchesDeleting: '批量删除',
  characterUnknownError: '未知错误',
  characterSaveSuccessfully: '保存成功',
  characterAddingCharacter: '正在添加角色',

  characterAddCharacterSuccessful: '添加新角色成功',
  characterAddNewUserFailure: '添加新用户失败',
  characterModifyingCharacter: '正在修改角色',
  characterModifyCharacterSuccessful: '修改角色成功',
  characterModifyCharacterFailure: '修改角色失败',
  characterDeletingCharacter: '正在删除角色',

  characterDeletingCharacterSuccessful: '删除角色成功',
  characterDeletingCharacterFailure: '删除角色失败',
  characterCharacterAddition: '新建角色',
  characterCodeTwo: '代码',
  characterEnterCodeLessThan50Characters: '请输入少于50个字符的代码！',
  characterEnterNameLessThan50Characters: '请输入少于50个字符的角色名！',

  characterPleaseChooseDepartment: '请选择部门',
  characterDataAuthority: '数据权限',
  characterDataAuthoritySelected: '已选数据权限',
  characterButtonAuthority: '按钮权限',
  characterModifyCharacterInformation: '修改角色信息',

  // 菜单管理
  menuOrder: '排序',
  menuID: '标识',
  menuCode: '代码',
  menuName: '名称',
  menuCreator: '创建人',
  menuCreationTime: '创建时间',
  menuModifier: '修改人',
  menuModificationTime: '更新时间',
  menuOptions: '操作',

  menuEdit: '编辑',
  menuConfirmDeleteAuthority: '确认删除该界面权限吗？',
  menuDelate: '删除',
  menuConfirm: '是',
  menuCancel: '否',
  menuAddition: '新建',
  menuExport: '导出',
  pageAuthority: '界面权限',
  pageCode: '界面权限代码',
  pageName: '界面权限名称',

  // 字典表管理
  dictionaryCode: '编码',
  dictionaryName: '字典名',
  dictionaryCreationTime: '创建时间',
  dictionaryModifier: '修改用户',
  dictionaryModificationTime: '修改时间',
  dictionaryDescription: '描述',
  dictionaryOptions: '操作',
  dictionaryDataManagement: '字典表管理',

  dictionaryDeleteContentsInBatches: '确认批量删除选中项吗？',
  dictionaryBatchesDeleting: '批量删除',
  dictionaryDeleteDictionaryTypes: '确认删除该字典类型吗？',
  dictionaryDictionaryContentSetup: '设置字典内容',
  dictionaryEdit: '编辑',
  dictionaryAddNewDictionarySuccessful: '添加新字典成功',
  dictionaryModifyDictionarySuccessful: '修改字典成功',
  dictionaryDeleteDictionarySuccessful: '删除字典成功',
  dictionaryAddDictionaryInformation: '新建字典信息',
  dictionaryEnterCodeLessThan50Characters: '请输入少于50个字符的代码！',
  dictionaryEnterNameLessThan50Characters: '请输入少于50个字符的角色名！',
  dictionaryEnterDescriptionLessThan50Characters: '请输入少于50个字符的描述！',
  dictionaryId: '标识',
  dictionaryNameTwo: '名称',
  dictionaryEnterAddressName: '请输入地址名称',
  dictionaryEnterCode: '请输入编码',
  dictionaryEnterValue: '请输入值',
  dictionaryValue: '值',
  dictionaryOrder: '次序',
  dictionaryEnterOrder: '请输入次序',
  dictionaryCreator: '创建人',
  dictionaryDelete: '删除',
  dictionaryDictionaryContent: '字典内容',
  dictionaryModifyDictionaryInformation: '修改字典信息',

  // 数据权限管理
  dataAuthorityEnable: '激活',
  dataAuthorityDisable: '禁用',
  dataAuthorityCode: '编码',
  dataAuthorityDetail: '描述',
  dataAuthorityStatus: '状态',
  dataAuthorityDepartment: '所属部门',
  dataAuthorityCreateUser: '创建用户',
  dataAuthorityCreateTime: '创建时间',
  dataAuthorityModifyUser: '修改用户',
  dataAuthorityModifyTime: '修改时间',
  dataAuthorityOptions: '操作',
  dataAuthorityDisableDataAuthority: '确认禁用该数据权限吗？',
  dataAuthorityDisableSuccessful: '禁用数据权限成功',
  dataAuthorityDisableFailure: '禁用数据权限失败',
  dataAuthorityConfirm: '是',
  dataAuthorityCancel: '否',
  dataAuthorityEnableAuthority: '确认激活该数据权限吗？',
  dataAuthorityEnableSuccessful: '激活数据权限成功',
  dataAuthorityEnableFailure: '激活数据权限失败',
  dataAuthorityManage:'数据权限管理',

  // 个人页
  basicSetting: '基本设置',
  basicLoginID: '登录名',
  basicFullName: '姓名',
  basicPhoneNumber: '手机号码',
  basicProfilePhoto: '头像',
  basicUpdateProfilePhoto: '更新头像',

  basicUploadImg: '只能上传JPG/PNG格式图片!',
  basicImg2MB: '图片大小必须小于2MB!',
  basicProfilePictureUploadedSuccessfully: '头像上传成功',
  basicInformationUpdatedSuccessfully: '更新基本信息成功',
  basicInformationUpdated: '更新基本信息',
  basicEmailAddressFormatIsIncorrect: '邮箱地址格式错误',
  basicPhoneNumberFormatIncorrect: '手机号格式错误',
  basicPleaseEnterNameLessThan50Characters: '请输入少于50个字符姓名！',
  // 安全设置
  securitySetting: '安全设置',
  securityAccountPassword: '账户密码',
  securityCurrentPasswordStrength: '当前密码强度',
  Strong: '强',
  Medium: '中',
  Low: '弱',
  securityEdit: '修改',
  securityEditSuccess: '密码修改成功',

  securityChangePassword: '修改密码',

  // 密码修改
  currentPassword: '当前密码',
  newPassword: '新密码',
  confirmPassword: '确认密码',

  enterPassword: '请输入当前密码！',
  passwordIncorrect: '当前密码不正确！',
  userInformationIsInvalid: '当前用户信息失效！',
  pleaseEnterNewPassword: '请输入新密码！',
  cannotBeThePreviousPassword: '不能为原密码！',
  pleaseConfirmNewPassword: '请确认新密码！',
  enteredPasswordsDiffer: '两次密码不一致！',
  systemError: '系统异常',

  // 退出登录
  signOut: '退出登录',
};
