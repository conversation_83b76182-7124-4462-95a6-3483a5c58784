// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 此处后端没有提供注释 POST /oauth/authorize */
export async function approveOrDeny(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.approveOrDenyParams,
  options?: { [key: string]: any },
) {
  return request<API.View>(`/auth/oauth/authorize`, {
    method: 'POST',
    params: {
      ...params,
      approvalParameters: undefined,
      ...params['approvalParameters'],
    },
    ...(options || {}),
  });
}
