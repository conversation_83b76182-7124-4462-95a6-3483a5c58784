import React, { useState } from 'react';
import { DatePicker, Space, Form, Select, Button, Card, Table, Row, Col, message, Divider } from 'antd';
import moment from 'moment';
import { queryGetqueueAnalysis, queryGetLoadunloadQueueAnalysis } from '@/services/ascAutoDigitalwin/api';

const { Option } = Select;

interface QueryParams {
  year: moment.Moment;
  month: string[];
  day: string[];
  team: string[];
  shift_number: string[];
}

// 生成1-12月的选项
const monthOptions = Array.from({ length: 12 }, (_, i) => ({
  label: `${i + 1}月`,
  value: `${i + 1}`,
}));

// 生成1-31日的选项
const dayOptions = Array.from({ length: 31 }, (_, i) => ({
  label: `${i + 1}日`,
  value: `${i + 1}`,
}));

const TruckQueueAnalysis: React.FC = () => {
  const [form] = Form.useForm();
  const [teamOptions, setTeamOptions] = useState<{ label: string; value: string }[]>([]);
  const [isVisiable, setIsVisiable] = useState<boolean>(false);
  const [qcTableData, setQcTableData] = useState<any>([])
  const [voyageTableData, setVoyageTableData] = useState<any>([])
  const [vesselNameVoyOptions, setVesselNameVoyOptions] = useState<any>([])
  const [currentSearchValues, setCurrentSearchValues] = useState<any>()
  const [truckOperationData, setTruckOperationData] = useState<any>([])
  const onFinish = (values: QueryParams) => {
    const formattedValues = {
      ...values,
      year: values.year.format('YYYY'),
    };
    setCurrentSearchValues(formattedValues)
    fetchQueryQueueAnalysis(formattedValues)
  };
  const fetchQueryQueueAnalysis = async (params: any) => {
    const result = await queryGetqueueAnalysis({
      year: params?.year || '',
      month: params?.month || [],
      day: params?.day || [],
      team: params?.team || [],
      shift_number: params?.shift_number || []
    })
    if (result && result.success) {
      if (Object.keys(result?.data)?.length) {
        const options = result?.data?.map((item: any) => ({
          label: item,
          value: item
        }))
        setVesselNameVoyOptions(options)
        const initialValue = result?.data
        fetchQueryLoadUnloadQueue(params, initialValue)
        setIsVisiable(true);
        message.info('数据查询成功！')
      } else {
        message.info('查询结果为空！')
        setVesselNameVoyOptions([])
        setQcTableData([])
        setVoyageTableData([])
      }
    } else {
      message.error('查询失败')
      setVesselNameVoyOptions([])
      setQcTableData([])
      setVoyageTableData([])
    }
  }
  const fetchQueryLoadUnloadQueue = async (params: any, vesselVoyList: any) => {
    const result = await queryGetLoadunloadQueueAnalysis({
      year: params?.year || '',
      month: params?.month || [],
      day: params?.day || [],
      team: params?.team || [],
      shift_number: params?.shift_number || [],
      vessel_name_voy: vesselVoyList || []
    })
    if (result && result.success) {
      const queueData = result?.data?.queue_data;
      const qcList = queueData?.filter((item: any) => item?.region_type === '作业路');
      const voyList = queueData?.filter((item: any) => item?.region_type === '箱区');
      setQcTableData(qcList)
      setVoyageTableData(voyList)
      setTruckOperationData([result?.data?.truck_operation_data])
    }
  }
  const fetchTeamOptions = async () => {
    try {
      const response = {
        data: ["1", "2", "3", "4"]
      };
      const options = response.data.map((team) => ({
        label: team,
        value: team
      }));
      setTeamOptions(options);
    } catch (error) {
      console.error('获取班组数据失败:', error);
    }
  };

  React.useEffect(() => {
    fetchTeamOptions();
  }, []);

  // 导出CSV功能
  const exportToCSV = () => {
    if (!qcTableData.length) {
      message.warning('暂无数据可导出');
      return;
    }
    const truckHeader = [
      '周转时间\t\t\t',
      '重车行驶时间\t\t\t\t\t',
      '空驶时间\t\t\t',
    ]
    const qcHeader = [
      '桥吊\t\t\t',
      '装船车次\t\t',
      '卸船车次\t\t\t',
      '装船平均等待时间\t\t\t\t',
      '卸船平均等待时间\t\t\t',
    ]
    const voyHeader = [
      '箱区\t\t\t',
      '装船车次\t\t',
      '卸船车次\t\t\t',
      '装船平均等待时间\t\t\t\t',
      '卸船平均等待时间\t\t\t',
    ]
    let csvContent = truckHeader.join(',') + '\n';
    truckOperationData?.forEach((item: any) => {
      const row = [
        (item?.truck_cycle_time ?? '') + '\t\t\t',
        (item?.truck_load_time ?? '') + '\t\t\t',
        (item?.truck_noload_time ?? '') + '\t\t\t',
      ]
      csvContent += row.join(',') + '\n';
    })
    csvContent = csvContent + '\n\t\t\t,\t\t\t,内集卡等待时间详情\t\t\t\t\t\t\t' + '\n';
    csvContent = csvContent + qcHeader.join(',') + '\n';
    qcTableData?.forEach((item: any) => {
      const row = [
        (item.region_id ?? '') + '\t\t\t',
        (item.load_number ?? '') + '\t\t\t',
        (item.unload_number ?? '') + '\t\t\t',
        (item.load_queue_time ?? '') + '\t\t\t',
        (item.unload_queue_time ?? '') + '\t\t\t',
      ]
      csvContent += row.join(',') + '\n';
    })
    csvContent = csvContent + voyHeader.join(',') + '\n';
    voyageTableData?.forEach((item: any) => {
      const row = [
        (item.region_id ?? '') + '\t\t\t',
        (item.load_number ?? '') + '\t\t\t',
        (item.unload_number ?? '') + '\t\t\t',
        (item.load_queue_time ?? '') + '\t\t\t',
        (item.unload_queue_time ?? '') + '\t\t\t',
      ]
      csvContent += row.join(',') + '\n';
    })

    // 创建Blob对象（使用UTF-16编码以更好地支持Excel）
    const blob = new Blob(['\ufeff' + csvContent], {
      type: 'text/csv;charset=utf-8;'
    });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    const fileName = `内集卡排队分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`;
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  const qcColumns = [
    {
      title: '桥吊',
      dataIndex: 'region_id',
      key: 'region_id',
    },
    {
      title: '装船车次',
      dataIndex: 'load_number',
      key: 'load_number',
    },
    {
      title: '卸船车次',
      dataIndex: 'unload_number',
      key: 'unload_number',
    },
    {
      title: '装船平均等待时间(分钟)',
      dataIndex: 'load_queue_time',
      key: 'load_queue_time',
    },
    {
      title: '卸船平均等待时间(分钟)',
      dataIndex: 'unload_queue_time',
      key: 'unload_queue_time',
    },
  ]

  const voyColumns = [
    {
      title: '箱区',
      dataIndex: 'region_id',
      key: 'region_id',
    },
    {
      title: '装船车次',
      dataIndex: 'load_number',
      key: 'load_number',
    },
    {
      title: '卸船车次',
      dataIndex: 'unload_number',
      key: 'unload_number',
    },
    {
      title: '装船平均等待时间(分钟)',
      dataIndex: 'load_queue_time',
      key: 'load_queue_time',
    },
    {
      title: '卸船平均等待时间(分钟)',
      dataIndex: 'unload_queue_time',
      key: 'unload_queue_time',
    },
  ]

  const truckColumns: any = [
    {
      title: '周转时间',
      dataIndex: 'truck_cycle_time',
      align: 'center',
    },
    {
      title: '重车行驶时间',
      dataIndex: 'truck_load_time',
      align: 'center',
    },
    {
      title: '空驶时间',
      dataIndex: 'truck_noload_time',
      align: 'center',
    }
  ]

  return (
    <Card>
      <Row>
        <Form
          form={form}
          layout="inline"
          onFinish={onFinish}
          style={{ marginBottom: 24 }}
        >
          <Form.Item
            name="year"
            label="年份"
            rules={[
              {
                required: true,
                message: '请选择年份！',
              },
            ]}
          >
            <DatePicker picker="year" format="YYYY" />
          </Form.Item>

          <Form.Item name="month" label="月份">
            <Select
              mode="multiple"
              placeholder="请选择月份"
              style={{ width: 200 }}
              options={monthOptions}
            />
          </Form.Item>

          <Form.Item name="day" label="日期">
            <Select
              mode="multiple"
              placeholder="请选择日期"
              style={{ width: 150 }}
              options={dayOptions}
            />
          </Form.Item>

          <Form.Item name="team" label="班组">
            <Select
              mode="multiple"
              placeholder="请选择班组"
              style={{ width: 150 }}
              options={teamOptions}
            />
          </Form.Item>

          <Form.Item name="shift_number" label="工班号">
            <Select mode="multiple" placeholder="请选择工班号" style={{ width: 200 }}>
              <Option value="1">1</Option>
              <Option value="2">2</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              查询
            </Button>
          </Form.Item>
          <Button
            type="primary"
            onClick={exportToCSV}
            disabled={!qcTableData?.length}
          >
            导出
          </Button>
        </Form>
      </Row>
      {isVisiable && (
        <Row>
          <Col>{'船舶-航次：'}</Col>
          <Col span={4}>
            <Select
              mode="multiple"
              showSearch
              placeholder="请选择船舶-航次"
              style={{ width: 300 }}
              key={JSON.stringify(vesselNameVoyOptions)}
              options={vesselNameVoyOptions}
              onChange={(value: any) => {
                fetchQueryLoadUnloadQueue(currentSearchValues, value)
              }}
            />
          </Col>
        </Row>
      )}
      <Table
        columns={truckColumns}
        dataSource={truckOperationData}
        rowKey={'index'}
        pagination={false}
        style={{ marginTop: 24 }}
        scroll={{ y: 50 }}
        summary={() => null}
        size='small'
      />
      <Divider />
      <Card
        type='inner'
        title="内集卡等待时间详情"
        styles={{
          header: {
            textAlign: 'center',
            color: 'red',
          },
        }}
      >
        <Table
          columns={qcColumns}
          dataSource={qcTableData}
          rowKey={'index'}
          pagination={false}
          style={{ marginTop: 1 }}
          scroll={{ y: 250 }}
          summary={() => null}
          size='small'
        />
        <Table
          columns={voyColumns}
          dataSource={voyageTableData}
          rowKey={'index'}
          pagination={false}
          style={{ marginTop: 10 }}
          scroll={{ y: 250 }}
          summary={() => null}
          size='small'
        />
      </Card>
    </Card>
  );
};

export default TruckQueueAnalysis;
