import React, { useState } from 'react';
import { DatePicker, Space, Form, Select, Button, Card, Table,Row,Col } from 'antd';
import moment from 'moment';
import { message } from 'antd';
import {queryGetTransportVolume} from '@/services/ascAutoDigitalwin/api'
const { Option } = Select;

interface QueryParams {
  year: moment.Moment | string | any;
  month: string[];
  day: string[];
  team: string[];
  shift_number: string[];
}
interface TableDataItem {
  intruck_operation_type?: string;
  intruck_amount: number;
  intruck_cost_time: number;
  intruck_actual_cost_time: number;
  intruck_transport_teu: number;
  intruck_transport_teu_per_hour:number;
  intruck_transport_unit: number;
  intruck_transport_unit_per_hour:number;
  team?: string;
  isTotal?: boolean;
}

const TruckTransportVolumn: React.FC = () => {
  const [form] = Form.useForm();
  const [tableData, setTableData] = useState<TableDataItem[]>([]);
  const [teamOptions, setTeamOptions] = useState<{ label: string; value: string }[]>([]);

  // 生成1-12月的选项
  const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    label: `${i + 1}月`,
    value: `${i + 1}`,
  }));

  // 生成1-31日的选项
  const dayOptions = Array.from({ length: 31 }, (_, i) => ({
    label: `${i + 1}日`,
    value: `${i + 1}`,
  }));

  const columns = [
    {
      title: '班组',
      dataIndex: 'team',
      key: 'team',
      render: (text: string, record: TableDataItem) => {
        if (record.isTotal) return <span style={{fontWeight:600}}>{'合计'}</span>;
        return text;
      },
      onCell: (record: TableDataItem, index: number) => {
        if (record.isTotal) {
          return { rowSpan: 1 };
        }
        // 获取当前班组的所有记录数（不包括合计行）
        const teamRecords = tableData.filter(
          item => item.team === record.team && !item.isTotal
        );
        // 如果是班组的第一条记录，设置 rowSpan
        if (index === tableData.findIndex(item => item.team === record.team)) {
          return { rowSpan: teamRecords.length};
        }
        return { rowSpan: 0 };
      }
    },
    {
      title: '作业方式',
      dataIndex: 'intruck_operation_type',
      key: 'intruck_operation_type',
      render: (text: string, record: TableDataItem) => {
        console.log('text',text,record);
        if(text === '合计'){
          return <span style={{fontWeight:600}}>{text}</span>
        }else{
          return text;
        }
      },
    },
    {
      title: '车次',
      dataIndex: 'intruck_amount',
      key: 'intruck_amount',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '理论耗时',
      dataIndex: 'intruck_cost_time',
      key: 'intruck_cost_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '实际耗时',
      dataIndex: 'intruck_actual_cost_time',
      key: 'intruck_actual_cost_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '内集卡拖运TEU',
      dataIndex: 'intruck_transport_teu',
      key: 'intruck_transport_teu',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '内集卡每小时拖运TEU',
      dataIndex: 'intruck_transport_teu_per_hour',
      key: 'intruck_transport_teu_per_hour',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '内集卡拖运UNIT',
      dataIndex: 'intruck_transport_unit',
      key: 'intruck_transport_unit',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '内集卡每小时拖运UNIT',
      dataIndex: 'intruck_transport_unit_per_hour',
      key: 'intruck_transport_unit_per_hour',
      render: (value: number) => value?.toFixed(2)
    },
  ] as const;

  // 查询接口
  const fetchQueryGetTransportVolume = async(params:QueryParams)=>{
    const result = await queryGetTransportVolume({
      year:params?.year || '',
      month:params?.month || [],
      day:params?.day || [],
      team:params?.team || [],
      shift_number:params?.shift_number || []
    });
    
    if(result && result.success){
      if(!Object?.keys(result?.data)?.length){
        setTableData([])
        return message.info('查询数据为空！')
      }else{
      const formattedData: TableDataItem[] = [];
      const teams = Object.keys(result.data).filter(key => 
        key !== 'success' && 
        key !== 'code' && 
        key !== 'message' && 
        key !== '合计'
      );
      teams.forEach(team => {
        if (Array.isArray(result.data[team])) {
          const teamData = result.data[team].map((item:any) => ({
            ...item,
            team: team
          }));
          formattedData.push(...teamData);
        }
      });

      // 添加合计行
      if (result.data['合计'] && Array.isArray(result.data['合计'])) {
        formattedData.push(...result.data['合计'].map((item:any) => ({
          ...item,
          intruck_operation_type:'',
          isTotal: true
        })));
      }
      setTableData(formattedData);
    }
    } else {
      message.error('查询失败！')
    }
  }
  const onFinish = (values: QueryParams) => {
    const formattedValues = {
      ...values,
      year: values.year.format('YYYY')
    };
    fetchQueryGetTransportVolume(formattedValues)
  };

  const fetchTeamOptions = async () => {
    try {
      const response = {
        data: ["1", "2", "3", "4"]
      };
      const options = response.data.map((team) => ({
        label: team,
        value: team
      }));
      setTeamOptions(options);
    } catch (error) {
      console.error('获取班组数据失败:', error);
    }
  };

  React.useEffect(() => {
    fetchTeamOptions();
  }, []);

  // 导出CSV功能
  const exportToCSV = () => {
    if (!tableData.length) {
      message.warning('暂无数据可导出');
      return;
    }
    // 处理数据，合并班组列
    const processedData: TableDataItem[][] = [];
    let currentTeam = '';
    let currentTeamData: TableDataItem[] = [];
    tableData.forEach((item: TableDataItem) => {
      if (item.isTotal) {
        // 处理合计行
        if (currentTeamData.length > 0) {
          processedData.push(currentTeamData);
          currentTeamData = [];
        }
        processedData.push([item]);
      } else if (item.team !== currentTeam) {
        // 新的班组开始
        if (currentTeamData.length > 0) {
          processedData.push(currentTeamData);
        }
        currentTeam = item.team || '';
        currentTeamData = [item];
      } else {
        // 同一班组的数据
        currentTeamData.push(item);
      }
    });
    // 添加最后一组数据
    if (currentTeamData.length > 0) {
      processedData.push(currentTeamData);
    }
    // 定义表头和列宽（使用制表符分隔以保持列宽）
    const headers = [
      '班组\t\t\t',
      '作业方式\t\t\t',
      '车次\t\t\t',
      '理论耗时\t\t\t',
      '实际耗时\t\t\t',
      '内集卡拖运TEU\t\t\t',
      '内集卡每小时拖运TEU\t\t\t',
      '内集卡拖运UNIT\t\t\t',
      '内集卡每小时拖运UNIT\t\t\t'
    ];

    // 生成CSV内容
    let csvContent = headers.join(',') + '\n';
    processedData.forEach(group => {
      group.forEach((item, index) => {
        const row = [
          index === 0 ? (item.isTotal ? '合计' : item.team) + '\t\t\t' : '\t\t\t',
          (item.intruck_operation_type || '') + '\t\t\t',
          item.intruck_amount.toFixed(2) + '\t\t\t',
          item.intruck_cost_time.toFixed(2) + '\t\t\t',
          item.intruck_actual_cost_time.toFixed(2) + '\t\t\t',
          item.intruck_transport_teu.toFixed(2) + '\t\t\t',
          item.intruck_transport_teu_per_hour.toFixed(2) + '\t\t\t',
          item.intruck_transport_unit.toFixed(2) + '\t\t\t',
          item.intruck_transport_unit_per_hour.toFixed(2) + '\t\t\t',
        ];
        csvContent += row.join(',') + '\n';
      });
    });
    // 创建Blob对象（使用UTF-16编码以更好地支持Excel）
    const blob = new Blob(['\ufeff' + csvContent], { 
      type: 'text/csv;charset=utf-8;'
    });
    
    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    const fileName = `内集卡拖运量统计_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`;
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <Card>
       <Row>
       <Form
        form={form}
        layout="inline"
        onFinish={onFinish}
        style={{ marginBottom: 24 }}
      >
        <Form.Item 
          name="year" 
          label="年份" 
          rules={[
            {
              required: true,
              message: '请选择年份！',
            },
          ]}
        >
          <DatePicker 
            picker="year" 
            format="YYYY"
          />
        </Form.Item>

        <Form.Item name="month" label="月份">
          <Select
            mode="multiple"
            placeholder="请选择月份"
            style={{ width: 200 }}
            options={monthOptions}
          />
        </Form.Item>

        <Form.Item name="day" label="日期">
          <Select
            mode="multiple"
            placeholder="请选择日期"
            style={{ width: 200 }}
            options={dayOptions}
          />
        </Form.Item>

        <Form.Item name="team" label="班组">
          <Select
            mode="multiple"
            placeholder="请选择班组"
            style={{ width: 150 }}
            options={teamOptions}
          />
        </Form.Item>

        <Form.Item name="shift_number" label="工班号">
          <Select
            mode="multiple"
            placeholder="请选择工班号"
            style={{ width: 150 }}
          >
            <Option value="1">1</Option>
            <Option value="2">2</Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit">
            查询
          </Button>
        </Form.Item>

          <Button 
            type="primary" 
            onClick={exportToCSV}
            disabled={!tableData.length}
          >
            导出
          </Button>
      </Form>
       </Row>
      <Table
        columns={columns}
        dataSource={tableData}
        rowKey={(record) => record.isTotal ? 'total' : `${record.team}-${record.intruck_operation_type}`}
        pagination={false}
        style={{ marginTop: 24 }}
        summary={() => null}
      />
    </Card>
  );
};

export default TruckTransportVolumn;