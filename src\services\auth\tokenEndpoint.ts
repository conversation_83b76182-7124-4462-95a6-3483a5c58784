// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 此处后端没有提供注释 GET /oauth/token */
export async function getAccessToken(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAccessTokenParams,
  options?: { [key: string]: any },
) {
  return request<API.OAuth2AccessToken>(`/base/oauth/token`, {
    method: 'GET',
    params: {
      ...params,
      parameters: undefined,
      ...params['parameters'],
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /oauth/token */
export async function postAccessToken(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAccessTokenParams,
  options?: { [key: string]: any },
) {
  return request<API.OAuth2AccessToken>(`/base/login`, {
    method: 'POST',
    params: {
      ...params,
      parameters: undefined,
      ...params['parameters'],
    },
    ...(options || {}),
    withCredentials: true
  });
}


export async function postLogin(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAccessTokenParams,
  headers: { [key: string]: any },
  options?: { [key: string]: any },
) {
  return request(`/base/login`, {
    method: 'POST',
    params: {
      ...params,
      parameters: undefined,
      ...params['parameters'],
    },
    headers: {
      ...headers,
    },
    ...(options || {}),
    withCredentials: true
  });
}

export async function postAuthorize(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAccessTokenParams,
  options?: { [key: string]: any },
) {
  return request(`/base/oauth2/authorize`, {
    method: 'GET',
    params: {
      ...params,
      parameters: undefined,
      ...params['parameters'],
    },
    ...(options || {}),
    withCredentials: true
  });
}

export async function getToken(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAccessTokenParams,
  headers: { [key: string]: any },
  options?: { [key: string]: any },
) {
  return request<API.OAuth2AccessToken>(`/base/oauth2/token`, {
    method: 'POST',
    params: {
      ...params,
      parameters: undefined,
      ...params['parameters'],
    },
    headers: {
      ...headers,
    },
    ...(options || {}),
    withCredentials: true
  });
}

