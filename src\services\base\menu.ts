// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 新增界面权限 新增界面权限 POST /v1/menu/addPermissionUI */
export async function addPermissionUi(
  body: API.PermissionUIAddCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/menu/addPermissionUI`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除界面权限 删除界面权限 POST /v1/menu/delPermissionUI */
export async function delPermissionUi(
  body: API.PermissionUIDelCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/menu/delPermissionUI`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取菜单信息 获取树状菜单信息 POST /v1/menu/findMenuTree */
export async function findMenuTree(options?: { [key: string]: any }) {
  return request<API.MultiApiResultMenuCO>(`/base/v1/menu/findMenuTree`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取界面权限信息 根据通用查询获取界面权限信息 POST /v1/menu/findPermissionUIPage */
export async function findPermissionUiPage(
  body: API.SimpleQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.PagedApiResultPermissionUICO>(`/base/v1/menu/findPermissionUIPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 排序界面权限 排序界面权限 POST /v1/menu/reOrderPermissionUI */
export async function reOrderPermissionUi(
  body: API.PermissionUIReOrderCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/menu/reOrderPermissionUI`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新界面权限 更新界面权限 POST /v1/menu/updatePermissionUI */
export async function updatePermissionUi(
  body: API.PermissionUIUpdateCmd,
  options?: { [key: string]: any },
) {
  return request<API.ApiResult>(`/base/v1/menu/updatePermissionUI`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
