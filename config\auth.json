{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://************:30001/auth", "description": "Generated server url"}], "paths": {"/oauth/token": {"get": {"tags": ["token-endpoint"], "operationId": "getAccessToken", "parameters": [{"name": "parameters", "in": "query", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OAuth2AccessToken"}}}}}}, "post": {"tags": ["token-endpoint"], "operationId": "postAccessToken", "parameters": [{"name": "parameters", "in": "query", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OAuth2AccessToken"}}}}}}}, "/oauth/authorize": {"post": {"tags": ["authorization-endpoint"], "operationId": "approveOr<PERSON>eny", "parameters": [{"name": "approvalParameters", "in": "query", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/View"}}}}}}}, "/oauth/token_key": {"get": {"tags": ["token-key-endpoint"], "operationId": "<PERSON><PERSON><PERSON>", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}}, "/oauth/check_token": {"get": {"tags": ["check-token-endpoint"], "operationId": "checkToken_6", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "put": {"tags": ["check-token-endpoint"], "operationId": "checkToken", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "post": {"tags": ["check-token-endpoint"], "operationId": "checkToken_5", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "delete": {"tags": ["check-token-endpoint"], "operationId": "checkToken_2", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "options": {"tags": ["check-token-endpoint"], "operationId": "checkToken_1", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "head": {"tags": ["check-token-endpoint"], "operationId": "checkToken_4", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "patch": {"tags": ["check-token-endpoint"], "operationId": "checkToken_3", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}, "components": {"schemas": {"OAuth2AccessToken": {"type": "object", "properties": {"value": {"type": "string"}, "expired": {"type": "boolean"}, "tokenType": {"type": "string"}, "additionalInformation": {"type": "object", "additionalProperties": {"type": "object"}}, "refreshToken": {"$ref": "#/components/schemas/OAuth2RefreshToken"}, "expiresIn": {"type": "integer", "format": "int32"}, "scope": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "expiration": {"type": "string", "format": "date-time"}}}, "OAuth2RefreshToken": {"type": "object", "properties": {"value": {"type": "string"}}}, "View": {"type": "object", "properties": {"contentType": {"type": "string"}}}}}}